package ${packageName}.admin.validate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
#foreach ($column in $columns)
#if ($column.javaType.equals("BigDecimal"))
import java.math.BigDecimal;
#end
#if ($column.javaType.equals("Date"))
import java.util.Date;
#end
#end
import javax.validation.constraints.*;

@Data
@ApiModel("${functionName}创建参数")
public class ${EntityName}CreateValidate implements Serializable {

    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if($column.isInsert==1)
    #if($column.isRequired==1)
    @NotNull(message = "${column.javaField}参数缺失")
    #end
    @ApiModelProperty(value = "${column.columnComment}")
    private ${column.javaType} ${column.javaField};

#end
#end
}
