package com.mdd.common.entity.beauty;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("ls_beauty_feed")
public class BeautyFeed implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer userId;
    private String title;
    private String content;
    private String images;
    private Integer likeNum;
    private Integer commentNum;
    private Integer status;
    private Long createTime;
    private Long updateTime;

    @TableLogic
    private Integer del;
} 