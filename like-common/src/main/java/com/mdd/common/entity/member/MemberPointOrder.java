package com.mdd.common.entity.member;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 积分订单实体
 */
@Data
@TableName("la_member_point_order")
public class MemberPointOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value="id", type= IdType.AUTO)
    private Integer id;            // 主键ID
    private String orderSn;        // 订单编号
    private Integer userId;        // 用户ID
    private Integer orderId;       // 关联订单ID
    private Integer payPoint;      // 支付积分
    private Integer status;        // 状态: [1=待付款, 2=已取消, 3=已付款, 4=已发货, 5=已完成]
    private Integer isDelete;      // 是否删除: [0=否, 1=是]
    private Long createTime;       // 创建时间
    private Long updateTime;       // 更新时间
    private Long deleteTime;       // 删除时间
} 