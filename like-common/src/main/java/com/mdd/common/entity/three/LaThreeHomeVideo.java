package com.mdd.common.entity.three;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("la_three_home_video")
public class LaThreeHomeVideo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String videoThumbnailAddress;

    private String videoAddress;

    private String videoSuffix;

    private String belongingBrand;

    private Integer isStop;

    private Integer isDelete;

    private Integer createTime;

    private Integer updateTime;

    private Integer deleteTime;
} 