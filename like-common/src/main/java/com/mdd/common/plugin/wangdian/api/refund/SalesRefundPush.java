package com.mdd.common.plugin.wangdian.api.refund;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.mdd.common.plugin.wangdian.WdtDriver;
import com.mdd.common.plugin.wangdian.dto.request.SalesRefundGoods;
import com.mdd.common.plugin.wangdian.dto.response.SalesRefundPushResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SalesRefundPush {

	/**
	 * 创建原始退款单
	 *
	 * @param client WdtDriver客户端
	 * @param refundGoodsList 销售退货信息列表
	 * @return 销售退货推送响应
	 */
	public static SalesRefundPushResponse execute(WdtDriver client, List<SalesRefundGoods> refundGoodsList) {
		// 将SalesRefundGoods对象列表转换为JSON字符串
		String api_refund_list_json = JSON.toJSONString(refundGoodsList);

		Map<String, String> params = new HashMap<String, String>();
		params.put("api_refund_list", api_refund_list_json);

		try {
			String response = client.execute("sales_refund_push.php", params);
			log.info("推送销售退货响应: {}", response);

			// 解析响应JSON为SalesRefundPushResponse对象
            return JSON.parseObject(response, SalesRefundPushResponse.class);
		} catch (IOException e) {
			log.error("推送销售退货异常", e);

			// 创建错误响应
			SalesRefundPushResponse errorResponse = new SalesRefundPushResponse();
			errorResponse.setCode(-1);
			errorResponse.setMessage("推送销售退货异常: " + e.getMessage());
			return errorResponse;
		}
	}

}
