package com.mdd.common.plugin.wangdian.bean.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 旺店通销售退货入库单推送请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockinRefundPushRequest {
    
    /**
     * 退货单列表节点
     * 请求参数的1级数据节点，包含销售退货单所有属性信息的数据节点
     */
    @JSONField(name = "stockin_refund_info")
    private StockinRefundInfo stockinRefundInfo;
    
    /**
     * 销售退货入库单信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StockinRefundInfo {
        
        /**
         * 退换单号
         * 系统退换单的单号，默认TK开头
         */
        @JSONField(name = "refund_no")
        private String refundNo;
        
        /**
         * 外部单号
         * 外部单据唯一标识，用于避免同一数据重复推送
         */
        @JSONField(name = "outer_no")
        private String outerNo;
        
        /**
         * 仓库编号
         * 代表仓库所有属性的唯一编码，用于仓库区分，ERP内支持自定义（ERP仓库界面设置），不支持推送多个仓库编号
         */
        @JSONField(name = "warehouse_no")
        private String warehouseNo;
        
        /**
         * 物流公司编号
         * 代表物流所有属性的唯一编码，用于物流区分，ERP内支持自定义（ERP物流界面设置）
         */
        @JSONField(name = "logistics_code")
        private String logisticsCode;
        
        /**
         * 物流单号
         */
        @JSONField(name = "logistics_no")
        private String logisticsNo;
        
        /**
         * 邮费
         */
        @JSONField(name = "post_fee")
        private BigDecimal postFee;
        
        /**
         * 其他费用
         */
        @JSONField(name = "other_fee")
        private BigDecimal otherFee;
        
        /**
         * 是否审核
         * 1：自动审核； 0：不自动审核  默认1
         */
        @JSONField(name = "is_check")
        private Integer isCheck;
        
        /**
         * 是否创建批次
         * 0：否；1：是  默认是0
         */
        @JSONField(name = "is_create_batch")
        private String isCreateBatch;
        
        /**
         * 备注
         */
        private String remark;
        
        /**
         * 货品列表节点
         * 请求参数的2级数据节点，包含销售退货入库单所有属性信息的数据节点
         */
        @JSONField(name = "detail_list")
        private List<DetailItem> detailList;
    }
    
    /**
     * 退货入库明细项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailItem {
        
        /**
         * 商家编码
         * 代表单品(sku)所有属性的唯一编码，ERP内单品唯一编码（sku）。推送的spec_no值在ERP货品档案必须存在，且上一层单据退换单也必须存在此编号，否则单据会创建失败。
         */
        @JSONField(name = "spec_no")
        private String specNo;
        
        /**
         * 货位
         * 商品在仓库内的位置编号，传值时货位编号为空，读ERP配置【入库开单货位优先级配置】，来决定取默认货位、上一次入库货位、ZANCUN货位其中一个货位。
         */
        @JSONField(name = "position_no")
        private String positionNo;
        
        /**
         * 批次号
         * 商品批次编号，ERP"批次管理"不存在推送的批次号，是否允许单据创建成功，并创建新的"批次号"0：否；1：是，默认是0。
         */
        @JSONField(name = "batch_no")
        private String batchNo;
        
        /**
         * 批次备注
         */
        @JSONField(name = "batch_remark")
        private String batchRemark;
        
        /**
         * 入库数量
         */
        @JSONField(name = "stockin_num")
        private BigDecimal stockinNum;
        
        /**
         * 单价
         * 为空或-1时，等于入库仓库的货品成本价
         */
        @JSONField(name = "stockin_price")
        private BigDecimal stockinPrice;
        
        /**
         * 生产日期
         * 格式：yyyy-MM-dd HH:mm:ss
         */
        @JSONField(name = "production_date")
        private String productionDate;
        
        /**
         * 有效期
         * 格式：yyyy-MM-dd HH:mm:ss
         */
        @JSONField(name = "expire_date")
        private String expireDate;
        
        /**
         * 税率
         */
        private BigDecimal tax;
        
        /**
         * 备注
         */
        private String remark;
        
        /**
         * 是否开启序列号
         * 是否开启序列号: 0代表不开启序列号    ＞0代表开启序列号    默认为0不开启
         */
        @JSONField(name = "is_enable_sn")
        private String isEnableSn;
        
        /**
         * 序列号列表
         * 序列号列表，多个序列号通过","隔开字符串区分，例如"xxx1,xxx2,xxx3"
         */
        @JSONField(name = "sn_list")
        private String snList;
    }
}
