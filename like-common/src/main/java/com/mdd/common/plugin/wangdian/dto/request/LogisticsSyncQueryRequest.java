package com.mdd.common.plugin.wangdian.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import static com.mdd.common.plugin.wangdian.Constant.SHOP_NO;

/**
 * 旺店通物流同步查询请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsSyncQueryRequest {

    /**
     * 获取条数
     * 每次请求需返回发货记录条数控制，取值范围大于等于，1小于等于100，limit输入值大于100时接口默认limit=100.
     */
    private Integer limit;

    /**
     * 店铺编号
     * 代表店铺所有属性的唯一编码，用于店铺区分，ERP内支持自定义（ERP店铺界面设置），用于获取指定店铺待同步数据信息
     */
    @JSONField(name = "shop_no")
    private String shopNo = SHOP_NO;

    /**
     * 是否支持拆单发货
     * 是否支持拆单发货 0 否 1 是（支持开启多物流单号回传的卖家，没有对应需求的卖家请不要传该字段）
     */
    @JSONField(name = "is_part_sync_able")
    private Integer isPartSyncAble;
}
