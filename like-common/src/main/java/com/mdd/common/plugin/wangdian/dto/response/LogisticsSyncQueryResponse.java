package com.mdd.common.plugin.wangdian.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 旺店通物流同步查询响应
 */
@Data
public class LogisticsSyncQueryResponse {

    /**
     * 状态码:0表示成功,其他表示失败
     */
    private Integer code;

    /**
     * 错误原因
     */
    private String message;

    /**
     * 数据条数
     * 待物流同步数据总条数
     */
    @JSONField(name = "total_count")
    private Integer totalCount;

    /**
     * 待同步物流订单信息列表
     * 响应参数的1级数据节点，包含当前页的订单待同步物流订单信息的数据节点
     */
    @JSONField(name = "trades")
    private List<LogisticsSyncItem> trades;

    /**
     * 获得当前同步记录的条数
     */
    @JSONField(name = "current_count")
    private Integer currentCount;


    /**
     * 旺店通物流同步项
     */
    @Data
    public static class LogisticsSyncItem {

        /**
         * 主键
         * 主键,用于logistics_sync_ack回写状态
         */
        @JSONField(name = "rec_id")
        private Integer recId;

        /**
         * 店铺编号
         * 代表店铺所有属性的唯一编码，用于店铺区分，ERP内支持自定义（ERP店铺界面设置）
         */
        @JSONField(name = "shop_no")
        private String shopNo;

        /**
         * 原始订单
         * 原始订单编号，商城或平台订单号
         */
        private String tid;

        /**
         * 物流单号
         * 物流或者快递面单对应的编号
         */
        @JSONField(name = "logistics_no")
        private String logisticsNo;

        /**
         * 物流方式
         * 响应值为代表物流方式的数字
         */
        @JSONField(name = "logistics_type")
        private Integer logisticsType;

        /**
         * 发货条件
         * 1款到发货 2货到付款(包含部分货到付款) 3分期付款
         */
        @JSONField(name = "delivery_term")
        private Integer deliveryTerm;

        /**
         * 发货时间
         * 发货时间    时间格式：yyyy-MM-dd HH:mm:ss
         */
        @JSONField(name = "consign_time")
        private String consignTime;

        /**
         * 是否拆分发货
         * 是否拆分发货,1:拆单发货,0:不进行拆单发货
         */
        @JSONField(name = "is_part_sync")
        private String isPartSync;

        /**
         * 原始子订单
         * 子订单编号串，以逗号(,) 分隔,(is_part_sync非0时才有效) 字段超过上限后会分多条trades返回
         */
        private String oids;

        /**
         * 平台ID
         * 平台ID，固定值127
         */
        @JSONField(name = "platform_id")
        private Integer platformId;

        /**
         * 订单ID
         */
        @JSONField(name = "trade_id")
        private Integer tradeId;

        /**
         * erp物流编号
         * 代表物流所有属性的唯一编码，用于物流区分，ERP内支持自定义（ERP物流界面设置）
         */
        @JSONField(name = "logistics_code_erp")
        private String logisticsCodeErp;

        /**
         * erp物流公司名称
         * ERP内自定义的物流名称，（对应ERP设置-基本设置-物流界面的物流名称）
         */
        @JSONField(name = "logistics_name_erp")
        private String logisticsNameErp;

        /**
         * 物流方式名称
         * 物流方式类型名称，（ERP设置-基本设置-物流界面的物流类型中文名称）
         */
        @JSONField(name = "logistics_name")
        private String logisticsName;

        /**
         * 出库单id
         */
        @JSONField(name = "stockout_id")
        private Integer stockoutId;

        /**
         * 是否同步
         * 1:需要同步,0:不需要同步
         */
        @JSONField(name = "is_need_sync")
        private Integer isNeedSync;

        /**
         * 同步状态
         * 0:等待同步2:同步失败,3:同步成功4:手动设置为同步成功,5:手动取消同步
         */
        @JSONField(name = "sync_status")
        private Integer syncStatus;

        /**
         * 是否需要多次同步
         * 一个订单要多次同步时，最后一个为1,用于同步回写状态
         */
        @JSONField(name = "is_last")
        private Integer isLast;

        /**
         * 店铺id
         */
        @JSONField(name = "shop_id")
        private Integer shopId;

        /**
         * 物流id
         */
        @JSONField(name = "logistics_id")
        private Integer logisticsId;

        /**
         * 描述
         * 同步说明
         */
        private String description;

        /**
         * 同步时间
         * 成功同步时间
         */
        @JSONField(name = "sync_time")
        private String syncTime;

        /**
         * 在线
         * 是否使用淘宝在线发货
         */
        @JSONField(name = "is_online")
        private Integer isOnline;

        /**
         * 错误码
         * 平台返回的错误码
         */
        @JSONField(name = "error_code")
        private String errorCode;

        /**
         * 错误描述
         * 平台返回的错误描述
         */
        @JSONField(name = "error_msg")
        private String errorMsg;

        /**
         * 重试次数
         * 重试计数(有些同步失败可重试)
         */
        @JSONField(name = "try_times")
        private Integer tryTimes;

        /**
         * 最后修改时间
         * 最后修改时间， 时间格式：yyyy-MM-dd HH:mm:ss
         */
        private String modified;

        /**
         * 创建时间
         * 创建时间，时间格式：yyyy-MM-dd HH:mm:ss
         */
        private String created;

        /**
         * 序列号
         * 序列号（存在序列号时返回序列号，无序列号时返回 flag）
         */
        @JSONField(name = "sn_list")
        private List<SerialNumberItem> snList;


    }


    /**
     * 旺店通序列号项
     */
    @Data
    public static class SerialNumberItem {

        /**
         * 子订单编号
         */
        private String oid;

        /**
         * 序列号
         */
        private String sn;
    }

}
