package com.mdd.common.plugin.wangdian.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

/**
 * 旺店通订单货品明细项
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItem {

    /**
     * 子订单编号
     * 平台订单货品表主键,子订单唯一标识,同一个sid下通过本接口新增订单的oid（子订单编号）要保证唯一；
     * 如果oid重复,ERP生成系统单（递交）时会提示"订单货品数量不一致xxxxxx"
     */
    private String oid;

    /**
     * 数量
     * 货品数量，订单推送成功以后本字段值不能更改
     */
    private BigDecimal num;

    /**
     * 单价
     * 标价,折扣前的价格,可以推送价格为0的商品。订单推送成功以后本字段值不能更改
     */
    private BigDecimal price;

    /**
     * 状态
     * 平台子订单状态，子订单状态可以和主订单不一样，比如其中一个子订单退款完成，其状态是80，但主订单仍然是待发货，可选值同trade_status
     */
    private Integer status;

    /**
     * 退款状态
     * 0:无退款,1:取消退款,2:已申请退款,3:等待退货,4:等待收货,5:退款成功。
     * 本字段在售前退款的时候，根据不同的场景填写不同的值，eg：申请退款值为2，取消退款值为1……
     */
    @JSONField(name = "refund_status")
    private Integer refundStatus;

    /**
     * 平台货品ID
     * 平台系统货品（SPU）的唯一标识。goods_id不能为空，goods_id和goods_no区别与SPU、SKU概念介绍
     */
    @JSONField(name = "goods_id")
    private String goodsId;

    /**
     * 平台规格ID
     * 平台系统单品（SKU）的的唯一标识，尽量不为空，spec_id和spec_no区别与SPU、SKU概念介绍
     */
    @JSONField(name = "spec_id")
    private String specId;

    /**
     * 货品编码
     * 平台货品SPU编码，对应ERP货品编号，尽量不为空
     */
    @JSONField(name = "goods_no")
    private String goodsNo;

    /**
     * 规格编码
     * 平台货品SKU唯一码，对应ERP商家编码，goods_no和spec_no不能同时为空
     */
    @JSONField(name = "spec_no")
    private String specNo;

    /**
     * 货品名称
     * 平台货品名称
     */
    @JSONField(name = "goods_name")
    private String goodsName;

    /**
     * 规格名称
     * 平台货品规格名称
     */
    @JSONField(name = "spec_name")
    private String specName;

    /**
     * 调整
     * 客服调整总金额(大于0加价，小于0减价，是折扣来源的一部分,没有传0)
     */
    @JSONField(name = "adjust_amount")
    private BigDecimal adjustAmount = BigDecimal.ZERO;

    /**
     * 优惠
     * 下单总折扣，客户下单时折扣(比如促销打折，不包含客服调整、分摊折扣，没有传0)
     */
    private BigDecimal discount = BigDecimal.ZERO;

    /**
     * 分摊优惠
     * 分摊总折扣，由总订单分摊而来，一般是付款时产生，如使用优惠券，没有传0。
     * 分摊优惠传值注意：例如三个商品，优惠10，分摊优惠可以是：3/3/4，或者3.33/3.33/3.34.
     * 即最后一个商品的分摊优惠使用减法计算
     */
    @JSONField(name = "share_discount")
    private BigDecimal shareDiscount = BigDecimal.ZERO;

    /**
     * 佣金
     * 佣金，不传默认为0
     */
    private BigDecimal commission;

    /**
     * 备注
     * 货品明细备注推送此字段ERP客户端需升级至V2.3.9.2及以上
     */
    private String remark;

    /**
     * 类目
     * 平台货品所属类目
     */
    private String cid;
}
