package com.mdd.common.plugin.wangdian.dto.request;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ErpGoods {

    private String goodsId;  // 字段: 货品ID, 类型: varchar, 长度: 40, 是否必填: 是, 描述: 平台服务货品主键，和创建原始订单时传入的货品ID一致
    private String specId;   // 字段: 规格ID, 类型: varchar, 长度: 40, 是否必填: 是, 描述: 平台服务货品规格id, 和创建原始订单时传入的规格ID一致
    private String goodsNo;  // 字段: 货品编码, 类型: varchar, 长度: 40, 是否必填: 是, 描述: 平台服务货品SPU码，尽量不要为空
    private String specNo;   // 字段: 规格编码, 类型: varchar, 长度: 40, 是否必填: 是, 描述: 平台服务SKU唯一码
    private Integer status;   // 字段: 状态, 类型: tinyint, 长度: 4, 是否必填: 是, 描述: 0删除 1在架 2下架

    private String goodsName; // 字段: 货品名称, 类型: varchar, 长度: 255, 是否必填: 否, 描述: 平台服务货品名称
    private String specCode; // 字段: 规格编码, 类型: varchar, 长度: 40, 是否必填: 否, 描述: 平台服务规格编码
    private String specName; // 字段: 规格名称, 类型: varchar, 长度: 100, 是否必填: 否, 描述: 平台服务规格名称
    private String picUrl;   // 字段: 图片url, 类型: varchar, 长度: 255, 是否必填: 否, 描述: 平台服务图片url
    private BigDecimal price; // 字段: 价格, 类型: decimal, 长度: 19,4, 是否必填: 否, 描述: 商品价格
    private BigDecimal stockNum; // 字段: 库存, 类型: decimal, 长度: 19,4, 是否必填: 否, 描述: 平台上商品的库存量
    private String cid;      // 字段: 平台类目, 类型: varchar, 长度: 40, 是否必填: 否, 描述: 平台服务商品所属类目
}
