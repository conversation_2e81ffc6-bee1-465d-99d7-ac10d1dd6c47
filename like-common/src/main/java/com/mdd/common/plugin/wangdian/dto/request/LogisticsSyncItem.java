package com.mdd.common.plugin.wangdian.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 旺店通物流同步状态回传项
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsSyncItem {
    
    /**
     * 回写的记录id
     * 回写的记录id，见logistics_sync_query中rec_id
     */
    @JSONField(name = "rec_id")
    private Integer recId;
    
    /**
     * 回写状态
     * 回写状态: 0成功 1失败
     */
    private Integer status = 0;
    
    /**
     * 相关描述信息
     * 相关描述信息,可在erp的物流同步界面看到
     */
    private String message;
}
