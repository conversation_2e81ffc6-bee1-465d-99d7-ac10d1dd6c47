package com.mdd.common.plugin.wangdian.dto.response;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;
import java.util.List;

@Data
public class StockoutOrderQueryTradeResponse {
    /**
     * 错误码
     */
    @JSONField(name = "code")
    private Integer code;

    /**
     * 错误描述
     */
    @JSONField(name = "message")
    private String message;

    /**
     * 数据条数
     */
    @JSONField(name = "total_count")
    private Integer totalCount;

    /**
     * 出库单节点
     */
    @JSONField(name = "stockout_list")
    private List<StockoutList> stockoutList;

    @Data
    public static class StockoutList {
        /**
         * 出库单主键id
         */
        @JSONField(name = "stockout_id")
        private Integer stockoutId;

        /**
         * 出库单号
         */
        @JSONField(name = "order_no")
        private String orderNo;

        /**
         * 源单号
         */
        @JSONField(name = "src_order_no")
        private String srcOrderNo;

        /**
         * 仓库编号
         */
        @JSONField(name = "warehouse_no")
        private String warehouseNo;

        /**
         * 发货时间
         */
        @JSONField(name = "consign_time")
        private Date consignTime;

        /**
         * 出库单类型
         */
        @JSONField(name = "order_type")
        private Integer orderType;

        /**
         * 订单状态
         */
        @JSONField(name = "trade_status")
        private Integer tradeStatus;

        /**
         * 出库单类型名称
         */
        @JSONField(name = "order_type_name")
        private String orderTypeName;

        /**
         * 订单类型
         */
        @JSONField(name = "trade_type")
        private Integer tradeType;

        /**
         * 出库子类型
         */
        @JSONField(name = "subtype")
        private String subtype;

        /**
         * 数量
         */
        @JSONField(name = "goods_count")
        private Double goodsCount;

        /**
         * 货品总售价
         */
        @JSONField(name = "goods_total_amount")
        private Double goodsTotalAmount;

        /**
         * 货品总成本
         */
        @JSONField(name = "goods_total_cost")
        private Double goodsTotalCost;

        /**
         * 邮资成本
         */
        @JSONField(name = "post_fee")
        private Double postFee;

        /**
         * 物流单号
         */
        @JSONField(name = "logistics_no")
        private String logisticsNo;

        /**
         * 包装费
         */
        @JSONField(name = "package_fee")
        private Double packageFee;

        /**
         * 收件人
         */
        @JSONField(name = "receiver_name")
        private String receiverName;

        /**
         * 国家
         */
        @JSONField(name = "receiver_country")
        private Integer receiverCountry;

        /**
         * 省份
         */
        @JSONField(name = "receiver_province")
        private String receiverProvince;

        /**
         * 城市
         */
        @JSONField(name = "receiver_city")
        private String receiverCity;

        /**
         * 地区
         */
        @JSONField(name = "receiver_district")
        private String receiverDistrict;

        /**
         * 应收金额
         */
        @JSONField(name = "receivable")
        private Double receivable;

        /**
         * 详细地址
         */
        @JSONField(name = "receiver_address")
        private String receiverAddress;

        /**
         * 收件人移动电话
         */
        @JSONField(name = "receiver_mobile")
        private String receiverMobile;

        /**
         * 收件人固定电话
         */
        @JSONField(name = "receiver_telno")
        private String receiverTelno;

        /**
         * 邮编
         */
        @JSONField(name = "receiver_zip")
        private String receiverZip;

        /**
         * 大头笔
         */
        @JSONField(name = "receiver_dtb")
        private String receiverDtb;

        /**
         * 实际重量
         */
        @JSONField(name = "weight")
        private Double weight;

        /**
         * 物流公司类型
         */
        @JSONField(name = "logistics_type")
        private String logisticsType;

        /**
         * 物流公司编号
         */
        @JSONField(name = "logistics_code")
        private String logisticsCode;

        /**
         * 物流公司名称
         */
        @JSONField(name = "logistics_name")
        private String logisticsName;

        /**
         * 打印备注
         */
        @JSONField(name = "print_remark")
        private String printRemark;

        /**
         * 已付金额
         */
        @JSONField(name = "paid")
        private Double paid;

        /**
         * 退款状态
         */
        @JSONField(name = "refund_status")
        private Integer refundStatus;

        /**
         * 业务员编号
         */
        @JSONField(name = "salesman_no")
        private String salesmanNo;

        /**
         * 业务员名称
         */
        @JSONField(name = "salesman_name")
        private String salesmanName;

        /**
         * 业务员名称
         */
        @JSONField(name = "fullname")
        private String fullname;

        /**
         * 仓库名称
         */
        @JSONField(name = "warehouse_name")
        private String warehouseName;

        /**
         * 创建时间
         */
        @JSONField(name = "created")
        private Date created;

        /**
         * 备注
         */
        @JSONField(name = "remark")
        private String remark;

        /**
         * 出库原因
         */
        @JSONField(name = "stockout_reason")
        private String stockoutReason;

        /**
         * 外部单号
         */
        @JSONField(name = "outer_no")
        private String outerNo;

        /**
         * 订单编号
         */
        @JSONField(name = "trade_no")
        private String tradeNo;

        /**
         * 原始单号
         */
        @JSONField(name = "src_trade_no")
        private String srcTradeNo;

        /**
         * 网名
         */
        @JSONField(name = "nick_name")
        private String nickName;

        /**
         * 客户名称
         */
        @JSONField(name = "customer_name")
        private String customerName;

        /**
         * 客户编号
         */
        @JSONField(name = "customer_no")
        private String customerNo;

        /**
         * 下单时间
         */
        @JSONField(name = "trade_time")
        private Date tradeTime;

        /**
         * 付款时间
         */
        @JSONField(name = "pay_time")
        private Date payTime;

        /**
         * 出库单状态
         */
        @JSONField(name = "status")
        private Integer status;

        /**
         * 店铺名称
         */
        @JSONField(name = "shop_name")
        private String shopName;

        /**
         * 店铺编号
         */
        @JSONField(name = "shop_no")
        private String shopNo;

        /**
         * 买家留言
         */
        @JSONField(name = "buyer_message")
        private String buyerMessage;

        /**
         * 客服备注
         */
        @JSONField(name = "cs_remark")
        private String csRemark;

        /**
         * 订单标记
         */
        @JSONField(name = "flag_name")
        private String flagName;

        /**
         * 邮费
         */
        @JSONField(name = "post_amount")
        private Double postAmount;

        /**
         * 截停原因
         */
        @JSONField(name = "block_reason")
        private Integer blockReason;

        /**
         * 发票类别
         */
        @JSONField(name = "invoice_type")
        private Integer invoiceType;

        /**
         * 发票抬头
         */
        @JSONField(name = "invoice_title")
        private String invoiceTitle;

        /**
         * 发票内容
         */
        @JSONField(name = "invoice_content")
        private String invoiceContent;

        /**
         * 发票ID
         */
        @JSONField(name = "invoice_id")
        private Integer invoiceId;

        /**
         * 发货条件
         */
        @JSONField(name = "delivery_term")
        private Integer deliveryTerm;

        /**
         * 分销类别
         */
        @JSONField(name = "fenxiao_type")
        private Integer fenxiaoType;

        /**
         * 分销商信息
         */
        @JSONField(name = "fenxiao_nick")
        private String fenxiaoNick;

        /**
         * 货到付款金额
         */
        @JSONField(name = "cod_amount")
        private Double codAmount;

        /**
         * 证件类别
         */
        @JSONField(name = "id_card_type")
        private Integer idCardType;

        /**
         * 收件人地址
         */
        @JSONField(name = "receiver_area")
        private String receiverArea;

        /**
         * 店铺备注
         */
        @JSONField(name = "shop_remark")
        private String shopRemark;

        /**
         * 最后修改时间
         */
        @JSONField(name = "modified")
        private Date modified;

        /**
         * 平台id
         */
        @JSONField(name = "platform_id")
        private Integer platformId;

        /**
         * 订单表主键
         */
        @JSONField(name = "trade_id")
        private Integer tradeId;

        /**
         * 审单员
         */
        @JSONField(name = "checker_name")
        private String checkerName;

        /**
         * 审单员编号
         */
        @JSONField(name = "employee_no")
        private String employeeNo;

        /**
         * 打包员编号
         */
        @JSONField(name = "packager_no")
        private String packagerNo;

        /**
         * 打包员
         */
        @JSONField(name = "packager_name")
        private String packagerName;

        /**
         * 拣货员编号
         */
        @JSONField(name = "picker_no")
        private String pickerNo;

        /**
         * 拣货员
         */
        @JSONField(name = "picker_name")
        private String pickerName;

        /**
         * 打单员编号
         */
        @JSONField(name = "printer_no")
        private String printerNo;

        /**
         * 打单员
         */
        @JSONField(name = "printer_name")
        private String printerName;

        /**
         * 验货员编号
         */
        @JSONField(name = "examiner_no")
        private String examinerNo;

        /**
         * 验货员
         */
        @JSONField(name = "examiner_name")
        private String examinerName;

        /**
         * 出库单号
         */
        @JSONField(name = "stockout_no")
        private String stockoutNo;

        /**
         * 源单据类别
         */
        @JSONField(name = "src_order_type")
        private String srcOrderType;

        /**
         * 处理状态
         */
        @JSONField(name = "wms_status")
        private String wmsStatus;

        /**
         * 接口处理错误信息
         */
        @JSONField(name = "error_info")
        private String errorInfo;

        /**
         * 仓库类别
         */
        @JSONField(name = "warehouse_type")
        private Integer warehouseType;

        /**
         * 出库仓库id
         */
        @JSONField(name = "warehouse_id")
        private Integer warehouseId;

        /**
         * 客户id
         */
        @JSONField(name = "customer_id")
        private String customerId;

        /**
         * 冻结原因
         */
        @JSONField(name = "freeze_reason")
        private Integer freezeReason;

        /**
         * 是否分配
         */
        @JSONField(name = "is_allocated")
        private Integer isAllocated;

        /**
         * 出库状态
         */
        @JSONField(name = "consign_status")
        private String consignStatus;

        /**
         * 电子面单状态
         */
        @JSONField(name = "ebill_status")
        private Integer ebillStatus;

        /**
         * 制单人
         */
        @JSONField(name = "operator_id")
        private String operatorId;

        /**
         * 货品种类数量
         */
        @JSONField(name = "goods_type_count")
        private Integer goodsTypeCount;

        /**
         * 主单实际应收
         */
        @JSONField(name = "actual_receivable")
        private Double actualReceivable;

        /**
         * MD5字符串值
         */
        @JSONField(name = "md5_str")
        private String md5Str;

        /**
         * 内置字段
         */
        @JSONField(name = "raw_goods_count")
        private Double rawGoodsCount;

        /**
         * 其他出库自定义子类别
         */
        @JSONField(name = "custom_type")
        private Integer customType;

        /**
         * 区域
         */
        @JSONField(name = "receiver_ring")
        private String receiverRing;

        /**
         * 配送时间
         */
        @JSONField(name = "to_deliver_time")
        private String toDeliverTime;

        /**
         * 配送中心
         */
        @JSONField(name = "pre_charge_time")
        private String preChargeTime;

        /**
         * 物流公司ID
         */
        @JSONField(name = "logistics_id")
        private Integer logisticsId;

        /**
         * 未知成本销售总额
         */
        @JSONField(name = "unknown_goods_amount")
        private Double unknownGoodsAmount;

        /**
         * 预估邮费成本
         */
        @JSONField(name = "calc_post_cost")
        private Double calcPostCost;

        /**
         * 邮费成本
         */
        @JSONField(name = "post_cost")
        private Double postCost;

        /**
         * 预估重量
         */
        @JSONField(name = "calc_weight")
        private Double calcWeight;

        /**
         * 快递重量
         */
        @JSONField(name = "post_weight")
        private Double postWeight;

        /**
         * 包装id
         */
        @JSONField(name = "package_id")
        private String packageId;

        /**
         * 包装成本
         */
        @JSONField(name = "package_cost")
        private Double packageCost;

        /**
         * 是否包含发票
         */
        @JSONField(name = "has_invoice")
        private Integer hasInvoice;

        /**
         * 打单员
         */
        @JSONField(name = "printer_id")
        private String printerId;

        /**
         * 拣货出错次数
         */
        @JSONField(name = "pick_error_count")
        private String pickErrorCount;

        /**
         * 拣货员id
         */
        @JSONField(name = "picker_id")
        private String pickerId;

        /**
         * 分拣员id
         */
        @JSONField(name = "sorter_id")
        private String sorterId;

        /**
         * 验货员id
         */
        @JSONField(name = "examiner_id")
        private String examinerId;

        /**
         * 发货人id
         */
        @JSONField(name = "consigner_id")
        private String consignerId;

        /**
         * 打包员id
         */
        @JSONField(name = "packager_id")
        private String packagerId;

        /**
         * 打包积分
         */
        @JSONField(name = "pack_score")
        private String packScore;

        /**
         * 拣货积分
         */
        @JSONField(name = "pick_score")
        private String pickScore;

        /**
         * 签出员工id
         */
        @JSONField(name = "checkouter_id")
        private String checkouterId;

        /**
         * 监视员id
         */
        @JSONField(name = "watcher_id")
        private String watcherId;

        /**
         * 分拣单编号
         */
        @JSONField(name = "picklist_no")
        private String picklistNo;

        /**
         * 分拣序号
         */
        @JSONField(name = "picklist_seq")
        private Integer picklistSeq;

        /**
         * 发票打印状态
         */
        @JSONField(name = "invoice_print_status")
        private Integer invoicePrintStatus;

        /**
         * 颜色
         */
        @JSONField(name = "flag_id")
        private Integer flagId;

        /**
         * 物流单模板id
         */
        @JSONField(name = "logistics_template_id")
        private Integer logisticsTemplateId;

        /**
         * 发货单模板id
         */
        @JSONField(name = "sendbill_template_id")
        private Integer sendbillTemplateId;

        /**
         * 货位分配方式
         */
        @JSONField(name = "pos_allocate_mode")
        private Integer posAllocateMode;

        /**
         * 便签条数
         */
        @JSONField(name = "note_count")
        private Integer noteCount;

        /**
         * 其他出库原因
         */
        @JSONField(name = "reason_id")
        private String reasonId;

        /**
         * 锁定策略id
         */
        @JSONField(name = "lock_id")
        private String lockId;

        /**
         * 保留
         */
        @JSONField(name = "reserve")
        private String reserve;

        /**
         * 源单据id
         */
        @JSONField(name = "src_order_id")
        private Integer srcOrderId;

        /**
         * 大头笔
         */
        @JSONField(name = "stockout_receiver_dtb")
        private String stockoutReceiverDtb;

        /**
         * 收件人的省份
         */
        @JSONField(name = "receiver_province_code")
        private Integer receiverProvinceCode;

        /**
         * 收件人的城市
         */
        @JSONField(name = "receiver_city_code")
        private Integer receiverCityCode;

        /**
         * 收件人的地区
         */
        @JSONField(name = "receiver_district_code")
        private Integer receiverDistrictCode;

        /**
         * 打印批次
         */
        @JSONField(name = "batch_no")
        private String batchNo;

        /**
         * 优惠金额
         */
        @JSONField(name = "discount")
        private Double discount;

        /**
         * 原始单号
         */
        @JSONField(name = "src_tids")
        private String srcTids;

        /**
         * 税额
         */
        @JSONField(name = "tax")
        private Double tax;

        /**
         * 税率
         */
        @JSONField(name = "tax_rate")
        private Double taxRate;

        /**
         * 币种
         */
        @JSONField(name = "currency")
        private String currency;

        /**
         * 证件号码
         */
        @JSONField(name = "id_card")
        private String idCard;

        /**
         * 出库单审核时间
         */
        @JSONField(name = "stock_check_time")
        private Date stockCheckTime;

        /**
         * 异常状态
         */
        @JSONField(name = "bad_reason")
        private Integer badReason;

        /**
         * 打印批次
         */
        @JSONField(name = "print_batch_no")
        private String printBatchNo;

        /**
         * 发货单打印状态
         */
        @JSONField(name = "sendbill_print_status")
        private Integer sendbillPrintStatus;

        /**
         * 物流单打印状态
         */
        @JSONField(name = "logistics_print_status")
        private Integer logisticsPrintStatus;

        /**
         * 分拣单打印状态
         */
        @JSONField(name = "picklist_print_status")
        private Integer picklistPrintStatus;

        /**
         * 制单人名称
         */
        @JSONField(name = "operator_name")
        private String operatorName;

        /**
         * 分销原始单号
         */
        @JSONField(name = "fenxiao_tid")
        private String fenxiaoTid;

        /**
         * 货品列表节点
         */
        @JSONField(name = "details_list")
        private List<DetailsList> detailsList;

        @Data
        public static class DetailsList {
            /**
             * 出库明细主键
             */
            @JSONField(name = "rec_id")
            private Integer recId;

            /**
             * 出库单主键id
             */
            @JSONField(name = "stockout_id")
            private Integer stockoutId;

            /**
             * 商家编码
             */
            @JSONField(name = "spec_no")
            private String specNo;

            /**
             * 销售价
             */
            @JSONField(name = "sell_price")
            private Double sellPrice;

            /**
             * 数量
             */
            @JSONField(name = "goods_count")
            private Double goodsCount;

            /**
             * 组合装数量
             */
            @JSONField(name = "suite_num")
            private Double suiteNum;

            /**
             * 品牌编号
             */
            @JSONField(name = "brand_no")
            private String brandNo;

            /**
             * 品牌名称
             */
            @JSONField(name = "brand_name")
            private String brandName;

            /**
             * 货品类型
             */
            @JSONField(name = "goods_type")
            private Integer goodsType;

            /**
             * 是否是赠品
             */
            @JSONField(name = "gift_type")
            private Integer giftType;

            /**
             * 货品名称
             */
            @JSONField(name = "goods_name")
            private String goodsName;

            /**
             * 货品编号
             */
            @JSONField(name = "goods_no")
            private String goodsNo;

            /**
             * 规格名称
             */
            @JSONField(name = "spec_name")
            private String specName;

            /**
             * 规格码
             */
            @JSONField(name = "spec_code")
            private String specCode;

            /**
             * 来源组合装编码
             */
            @JSONField(name = "suite_no")
            private String suiteNo;

            /**
             * 成本价
             */
            @JSONField(name = "cost_price")
            private Double costPrice;

            /**
             * 总货款
             */
            @JSONField(name = "total_amount")
            private Double totalAmount;

            /**
             * 货品id
             */
            @JSONField(name = "goods_id")
            private Integer goodsId;

            /**
             * 商品主键
             */
            @JSONField(name = "spec_id")
            private Integer specId;

            /**
             * 估重
             */
            @JSONField(name = "weight")
            private Double weight;

            /**
             * 备注
             */
            @JSONField(name = "remark")
            private String remark;

            /**
             * 已支付金额
             */
            @JSONField(name = "paid")
            private Double paid;

            /**
             * 退款状态
             */
            @JSONField(name = "refund_status")
            private Integer refundStatus;

            /**
             * 零售价
             */
            @JSONField(name = "market_price")
            private Double marketPrice;

            /**
             * 分销价
             */
            @JSONField(name = "fenxiao_amount")
            private Double fenxiaoAmount;

            /**
             * 总折扣金额
             */
            @JSONField(name = "discount")
            private Double discount;

            /**
             * 分摊后合计应收
             */
            @JSONField(name = "share_amount")
            private Double shareAmount;

            /**
             * 税率
             */
            @JSONField(name = "tax_rate")
            private Double taxRate;

            /**
             * 条码
             */
            @JSONField(name = "barcode")
            private String barcode;

            /**
             * 基本单位
             */
            @JSONField(name = "unit_name")
            private String unitName;

            /**
             * 订单详情表主键
             */
            @JSONField(name = "sale_order_id")
            private Integer saleOrderId;

            /**
             * 邮费分摊
             */
            @JSONField(name = "share_post")
            private Double sharePost;

            /**
             * 原始子订单号
             */
            @JSONField(name = "src_oid")
            private String srcOid;

            /**
             * 子单原始单号
             */
            @JSONField(name = "src_tid")
            private String srcTid;

            /**
             * 单据内部来源
             */
            @JSONField(name = "from_mask")
            private Integer fromMask;

            /**
             * 源单据类别
             */
            @JSONField(name = "src_order_type")
            private Integer srcOrderType;

            /**
             * sales_trade_order表的主键
             */
            @JSONField(name = "src_order_detail_id")
            private String srcOrderDetailId;

            /**
             * 基本单位
             */
            @JSONField(name = "base_unit_id")
            private Integer baseUnitId;

            /**
             * 平台标签
             */
            @JSONField(name = "from_mask_ext")
            private String fromMaskExt;

            /**
             * 辅助单位
             */
            @JSONField(name = "unit_id")
            private Integer unitId;

            /**
             * 单位换算
             */
            @JSONField(name = "unit_ratio")
            private Double unitRatio;

            /**
             * 货品数量
             */
            @JSONField(name = "num")
            private Double num;

            /**
             * 辅助数量
             */
            @JSONField(name = "num2")
            private Double num2;

            /**
             * 最终价格
             */
            @JSONField(name = "price")
            private Double price;

            /**
             * 指定出库批次
             */
            @JSONField(name = "batch_id")
            private String batchId;

            /**
             * 是否验货
             */
            @JSONField(name = "is_examined")
            private Integer isExamined;

            /**
             * 是否包装
             */
            @JSONField(name = "is_package")
            private Integer isPackage;

            /**
             * 是否允许0成本
             */
            @JSONField(name = "is_zero_cost")
            private Integer isZeroCost;

            /**
             * 扫描方式
             */
            @JSONField(name = "scan_type")
            private Integer scanType;

            /**
             * 创建时间
             */
            @JSONField(name = "created")
            private Date created;

            /**
             * 最后修改时间
             */
            @JSONField(name = "modified")
            private Date modified;

            /**
             * 子单实际已付金额
             */
            @JSONField(name = "actual_paid")
            private Double actualPaid;

            /**
             * 平台id
             */
            @JSONField(name = "platform_id")
            private Integer platformId;

            /**
             * 出库货位id
             */
            @JSONField(name = "position_id")
            private Integer positionId;

            /**
             * 货品档案自定义属性1
             */
            @JSONField(name = "good_prop1")
            private String goodProp1;

            /**
             * 货品档案自定义属性2
             */
            @JSONField(name = "good_prop2")
            private String goodProp2;

            /**
             * 货品档案自定义属性3
             */
            @JSONField(name = "good_prop3")
            private String goodProp3;

            /**
             * 货品档案自定义属性4
             */
            @JSONField(name = "good_prop4")
            private String goodProp4;

            /**
             * 货品档案自定义属性5
             */
            @JSONField(name = "good_prop5")
            private String goodProp5;

            /**
             * 货品档案自定义属性6
             */
            @JSONField(name = "good_prop6")
            private String goodProp6;

            /**
             * 单品列表自定义属性1
             */
            @JSONField(name = "prop1")
            private String prop1;

            /**
             * 单品列表自定义属性2
             */
            @JSONField(name = "prop2")
            private String prop2;

            /**
             * 单品列表自定义属性3
             */
            @JSONField(name = "prop3")
            private String prop3;

            /**
             * 单品列表自定义属性4
             */
            @JSONField(name = "prop4")
            private String prop4;

            /**
             * 单品列表自定义属性5
             */
            @JSONField(name = "prop5")
            private String prop5;

            /**
             * 单品列表自定义属性6
             */
            @JSONField(name = "prop6")
            private String prop6;

            /**
             * 批次号
             */
            @JSONField(name = "batch_no")
            private String batchNo;

            /**
             * 批次备注
             */
            @JSONField(name = "batch_remark")
            private String batchRemark;

            /**
             * 有效期
             */
            @JSONField(name = "expire_date")
            private Date expireDate;

            /**
             * 货位节点列表
             */
            @JSONField(name = "position_list")
            private List<PositionList> positionList;

            @Data
            public static class PositionList {
                /**
                 * 货位ID
                 */
                @JSONField(name = "position_id")
                private Integer positionId;

                /**
                 * 货位编号
                 */
                @JSONField(name = "position_no")
                private String positionNo;

                /**
                 * 批次备注
                 */
                @JSONField(name = "batch_remark")
                private String batchRemark;

                /**
                 * 出库单主键id
                 */
                @JSONField(name = "stockout_id")
                private Integer stockoutId;

                /**
                 * 出库单明细id
                 */
                @JSONField(name = "stockout_order_detail_id")
                private Integer stockoutOrderDetailId;

                /**
                 * 货位主键id
                 */
                @JSONField(name = "rec_id")
                private Integer recId;

                /**
                 * 入库单明细id
                 */
                @JSONField(name = "stockin_detail_id")
                private Integer stockinDetailId;

                /**
                 * 货品数量
                 */
                @JSONField(name = "num")
                private Double num;

                /**
                 * 入库单号明细id
                 */
                @JSONField(name = "stock_spec_detail_id")
                private Long stockSpecDetailId;

                /**
                 * 批次id
                 */
                @JSONField(name = "batch_id")
                private Integer batchId;

                /**
                 * 批次号
                 */
                @JSONField(name = "batch_no")
                private String batchNo;

                /**
                 * 最后更新时间
                 */
                @JSONField(name = "modified")
                private Date modified;

                /**
                 * 成本价
                 */
                @JSONField(name = "cost_price")
                private Double costPrice;

                /**
                 * 货位出库数量
                 */
                @JSONField(name = "position_goods_count")
                private Double positionGoodsCount;

                /**
                 * 有效期
                 */
                @JSONField(name = "expire_date")
                private Date expireDate;
            }
        }
    }
}
