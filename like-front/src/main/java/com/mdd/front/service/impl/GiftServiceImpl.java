package com.mdd.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mdd.common.core.PageResult;
import com.mdd.common.entity.gift.GiftOrder;
import com.mdd.common.entity.goods.GoodsSku;
import com.mdd.common.entity.order.Order;
import com.mdd.common.entity.order.OrderGoods;
import com.mdd.common.entity.user.User;
import com.mdd.common.entity.user.UserAddress;
import com.mdd.common.enums.OrderEnum;
import com.mdd.common.enums.OrderLogEnum;
import com.mdd.common.exception.OperateException;
import com.mdd.common.mapper.gift.GiftOrderMapper;
import com.mdd.common.mapper.goods.GoodsSkuMapper;
import com.mdd.common.mapper.log.LogOrderMapper;
import com.mdd.common.mapper.order.OrderGoodsMapper;
import com.mdd.common.mapper.order.OrderMapper;
import com.mdd.common.mapper.user.UserAddressMapper;
import com.mdd.common.mapper.user.UserMapper;
import com.mdd.common.util.ToolUtils;
import com.mdd.common.util.UrlUtils;
import com.mdd.front.LikeFrontThreadLocal;
import com.mdd.front.service.IGiftService;
import com.mdd.front.service.IOrderService;
import com.mdd.front.validate.common.PageValidate;
import com.mdd.front.validate.gift.GiftReceiveValidate;
import com.mdd.front.validate.gift.GiftSettleValidate;
import com.mdd.front.validate.gift.GiftConfirmReceiveValidate;
import com.mdd.front.validate.order.BuyGoodsValidate;
import com.mdd.front.validate.order.OrderSettleValidate;
import com.mdd.front.vo.gift.GiftDetailVo;
import com.mdd.front.vo.gift.GiftListVo;
import com.mdd.front.vo.gift.GiftReceiveVo;
import com.mdd.front.vo.gift.GiftSubmitResultVo;
import com.mdd.front.vo.order.OrderSettleResultVo;
import com.mdd.front.vo.order.OrderSubmitResultVo;
import com.mdd.front.vo.gift.GiftPaySuccessVo;
import com.mdd.front.vo.gift.GiftOrderDetailVo;
import com.mdd.front.validate.gift.GiftReceiveByCodeValidate;
import com.mdd.front.vo.gift.GiftSecurityCodeVo;
import com.mdd.front.vo.user.UserAddressVo;
import com.mdd.front.vo.gift.GiftQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 礼物赠送服务实现类
 */
@Slf4j
@Service
public class GiftServiceImpl implements IGiftService {

    @Resource
    private GiftOrderMapper giftOrderMapper;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderGoodsMapper orderGoodsMapper;

    @Resource
    private LogOrderMapper logOrderMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserAddressMapper userAddressMapper;

    @Resource
    private GoodsSkuMapper goodsSkuMapper;

    @Resource
    private IOrderService iOrderService;

    /**
     * 礼物结算
     *
     * @param settleValidate 礼物结算参数
     * @return 结算结果
     */
    @Override
    public GiftSubmitResultVo settlementGift(GiftSettleValidate settleValidate) {
        // 创建普通订单结算对象
        OrderSettleValidate orderSettleValidate = new OrderSettleValidate();
        
        // 手动设置必要字段，确保不会出现空值
        orderSettleValidate.setBuyType(settleValidate.getBuyType());
        orderSettleValidate.setCouponListId(settleValidate.getCouponListId());
        orderSettleValidate.setDeliveryType(settleValidate.getDeliveryType());
        orderSettleValidate.setSelffetchShopId(settleValidate.getSelffetchShopId());
        orderSettleValidate.setRemark(settleValidate.getRemark());
        
        // 创建BuyGoodsValidate对象
        BuyGoodsValidate buyGoodsValidate = new BuyGoodsValidate();
        buyGoodsValidate.setGoodsId(settleValidate.getGoodsId());
        buyGoodsValidate.setSkuId(settleValidate.getGoodsSkuId());
        buyGoodsValidate.setNum(settleValidate.getGoodsNum());
        buyGoodsValidate.setCartId(settleValidate.getCartId());
        
        // 设置订单商品信息
        List<BuyGoodsValidate> buyGoodsList = new ArrayList<>();
        buyGoodsList.add(buyGoodsValidate);
        orderSettleValidate.setBuyGoods(buyGoodsList);
        
        // 设置订单类型为礼物订单
        orderSettleValidate.setOrderType(2);
        
        // 调用订单结算服务
        OrderSettleResultVo settleResultVo = iOrderService.settlementOrder(orderSettleValidate);
        
        // 转换为礼物提交结果
        GiftSubmitResultVo result = new GiftSubmitResultVo();
        BeanUtils.copyProperties(settleResultVo, result);
        
        return result;
    }

    /**
     * 提交礼物订单
     *
     * @param settleValidate 礼物结算参数
     * @return 提交结果
     */
    @Override
    @Transactional
    public GiftSubmitResultVo submitGift(GiftSettleValidate settleValidate) {
        Integer userId = LikeFrontThreadLocal.getUserId();
        
        // 创建普通订单结算对象
        OrderSettleValidate orderSettleValidate = new OrderSettleValidate();
        
        // 手动设置必要字段，确保不会出现空值
        orderSettleValidate.setBuyType(settleValidate.getBuyType());
        orderSettleValidate.setCouponListId(settleValidate.getCouponListId());
        orderSettleValidate.setDeliveryType(settleValidate.getDeliveryType());
        orderSettleValidate.setSelffetchShopId(settleValidate.getSelffetchShopId());
        orderSettleValidate.setRemark(settleValidate.getRemark());
        
        // 创建BuyGoodsValidate对象
        BuyGoodsValidate buyGoodsValidate = new BuyGoodsValidate();
        buyGoodsValidate.setGoodsId(settleValidate.getGoodsId());
        buyGoodsValidate.setSkuId(settleValidate.getGoodsSkuId());
        buyGoodsValidate.setNum(settleValidate.getGoodsNum());
        buyGoodsValidate.setCartId(settleValidate.getCartId());
        
        // 设置订单商品信息
        List<BuyGoodsValidate> buyGoodsList = new ArrayList<>();
        buyGoodsList.add(buyGoodsValidate);
        orderSettleValidate.setBuyGoods(buyGoodsList);
        
        // 设置订单类型为礼物订单
        orderSettleValidate.setOrderType(2);
        
        // 调用订单提交服务
        OrderSubmitResultVo orderResult = iOrderService.submitOrder(orderSettleValidate);
        
        // 获取订单ID
        Integer orderId = orderResult.getOrderId();
        
        // 查询订单信息
        Order order = orderMapper.selectById(orderId);
        if (order != null) {
            // 更新订单地址信息为临时占位符
            order.setAddress("{}"); // 设置为空JSON对象
            order.setAddressContact("礼物订单");
            order.setAddressMobile("00000000000");
            order.setAddressContent("礼物订单临时地址，等待接收方填写");
            order.setUpdateTime(System.currentTimeMillis() / 1000);
            
            // 保存更新后的订单
            orderMapper.updateById(order);
        }
        
        // 创建赠送订单记录，此时不生成安全口令，等待支付成功后生成
        GiftOrder giftOrder = new GiftOrder();
        giftOrder.setOrderId(orderResult.getOrderId());
        giftOrder.setOrderSn(orderResult.getOrderSn());
        giftOrder.setFromUserId(userId);
        // 先设置为空，支付成功后再生成
        giftOrder.setSecurityCode("");
        giftOrder.setMessage(settleValidate.getMessage());
        giftOrder.setGiftStatus(OrderEnum.GIFT_STATUS_WAITING.getCode());
        
        // 生成赠送链接
        String giftLink = generateGiftLink(userId, orderResult.getOrderId());
        giftOrder.setGiftLink(giftLink);
        
        // 设置过期时间（30天后）
        Long currentTime = System.currentTimeMillis() / 1000;
        giftOrder.setExpireTime(currentTime + 30 * 24 * 60 * 60);
        giftOrder.setCreateTime(currentTime);
        giftOrder.setUpdateTime(currentTime);
        giftOrder.setIsDelete(0);
        
        // 保存支付方式信息，如果前端提供
        if (settleValidate.getPayWay() != null) {
            try {
                // 这里可以记录支付方式，如果需要的话
                if (order != null) {
                    order.setPayWay(settleValidate.getPayWay());
                    orderMapper.updateById(order);
                }
            } catch (NumberFormatException e) {
                log.error("支付方式转换错误: " + e.getMessage());
                // 转换失败不影响主流程，继续执行
            }
        }
        
        // 保存赠送订单
        giftOrderMapper.insert(giftOrder);
        
        // 记录订单日志
        logOrderMapper.add(orderResult.getOrderId(), 
                OrderLogEnum.TYPE_USER.getCode(), 
                OrderLogEnum.CHANNEL_ADD_ORDER.getCode(), 
                userId, 
                "创建赠送订单");
        
        // 转换为礼物提交结果
        GiftSubmitResultVo result = new GiftSubmitResultVo();
        BeanUtils.copyProperties(orderResult, result);
        result.setGiftId(giftOrder.getId());
        result.setGiftLink(giftOrder.getGiftLink());
        // 不返回安全口令，因为还未生成
        result.setSecurityCode("");
        
        return result;
    }

    /**
     * 支付结果通知
     *
     * @param orderId 订单ID
     * @param payStatus 支付状态 [1=支付成功, 0=支付取消/失败]
     * @return 处理结果
     */
    @Override
    @Transactional
    public boolean notifyPayStatus(Integer orderId, Integer payStatus) {
        // 查询订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null || order.getIsDelete() == 1) {
            log.error("支付结果通知: 订单不存在, orderId={}", orderId);
            return false;
        }
        
        // 查询礼物订单信息
        GiftOrder giftOrder = giftOrderMapper.selectOne(
                new QueryWrapper<GiftOrder>()
                        .eq("order_id", orderId)
                        .eq("is_delete", 0));
        
        if (giftOrder == null) {
            log.error("支付结果通知: 礼物订单不存在, orderId={}", orderId);
            return false;
        }
        
        // 如果支付成功
        if (payStatus == 1) {
            // 生成四位数字安全口令
            String securityCode = generateSecurityCode();
            giftOrder.setSecurityCode(securityCode);
            giftOrder.setUpdateTime(System.currentTimeMillis() / 1000);
            
            // 更新礼物订单
            giftOrderMapper.updateById(giftOrder);
            
            // 记录订单日志
            logOrderMapper.add(orderId, 
                    OrderLogEnum.TYPE_USER.getCode(), 
                    OrderLogEnum.CHANNEL_PAY_ORDER.getCode(),
                    giftOrder.getFromUserId(), 
                    "礼物订单支付成功，生成安全口令");
            
            return true;
        } else {
            // 支付取消/失败，可以做一些额外处理
            log.info("礼物订单支付取消/失败, orderId={}", orderId);
            return false;
        }
    }

    /**
     * 礼物订单详情
     *
     * @param id 礼物订单ID
     * @return 礼物订单详情
     */
    @Override
    public GiftDetailVo detail(Integer id) {
        GiftOrder giftOrder = giftOrderMapper.selectById(id);
        if (giftOrder == null || giftOrder.getIsDelete() == 1) {
            throw new OperateException("赠送订单不存在");
        }
        
        Order order = orderMapper.selectById(giftOrder.getOrderId());
        if (order == null || order.getIsDelete() == 1) {
            throw new OperateException("关联订单不存在");
        }
        
        // 查询订单商品
        List<OrderGoods> orderGoodsList = orderGoodsMapper.selectList(
                new QueryWrapper<OrderGoods>()
                        .eq("order_id", order.getId()));
        
        // 处理商品图片URL，转为绝对路径
        for (OrderGoods goods : orderGoodsList) {
            goods.setGoodsImage(UrlUtils.toAbsoluteUrl(goods.getGoodsImage()));
        }
        
        // 构建礼物订单详情
        GiftDetailVo detailVo = new GiftDetailVo();
        detailVo.setId(giftOrder.getId());
        detailVo.setOrderId(order.getId());
        detailVo.setOrderSn(order.getOrderSn());
        detailVo.setFromUserId(giftOrder.getFromUserId());
        
        // 获取当前登录用户ID
        Integer currentUserId = LikeFrontThreadLocal.getUserId();
        
        // 判断当前用户是赠送人还是接收人
        if (currentUserId != null) {
            if (currentUserId.equals(giftOrder.getFromUserId())) {
                detailVo.setUserRole(1); // 赠送人
            } else if (currentUserId.equals(giftOrder.getToUserId())) {
                detailVo.setUserRole(2); // 接收人
            } else {
                detailVo.setUserRole(0); // 其他人/未知
            }
        }
        
        // 获取赠送者信息
        User fromUser = userMapper.selectById(giftOrder.getFromUserId());
        if (fromUser != null) {
            detailVo.setFromUserNickname(fromUser.getNickname());
            detailVo.setFromUserAvatar(fromUser.getAvatar());
        }
        
        // 接收者信息
        if (giftOrder.getToUserId() != null) {
            detailVo.setToUserId(giftOrder.getToUserId());
            User toUser = userMapper.selectById(giftOrder.getToUserId());
            if (toUser != null) {
                detailVo.setToUserNickname(toUser.getNickname());
                detailVo.setToUserAvatar(toUser.getAvatar());
            }
        }
        
        detailVo.setSecurityCode(giftOrder.getSecurityCode());
        detailVo.setGiftLink(giftOrder.getGiftLink());
        detailVo.setMessage(giftOrder.getMessage());
        detailVo.setGiftStatus(giftOrder.getGiftStatus());
        detailVo.setGiftStatusMsg(OrderEnum.getGiftStatusMsg(giftOrder.getGiftStatus()));
        detailVo.setReceiveTime(giftOrder.getReceiveTime());
        detailVo.setExpireTime(giftOrder.getExpireTime());
        
        // 设置商品信息
        detailVo.setGoodsList(orderGoodsList);
        detailVo.setGoodsMoney(order.getGoodsMoney());
        detailVo.setOrderAmount(order.getNeedPayMoney());
        
        // 计算商品总重量
        BigDecimal totalWeight = BigDecimal.ZERO;
        for (OrderGoods goods : orderGoodsList) {
            try {
                // OrderGoods中可能没有商品重量信息，从GoodsSku表中获取
                if (goods.getGoodsSkuId() != null) {
                    // 查询商品SKU获取重量信息
                    GoodsSku goodsSku = goodsSkuMapper.selectById(goods.getGoodsSkuId());
                    if (goodsSku != null && goodsSku.getWeight() != null) {
                        totalWeight = totalWeight.add(BigDecimal.valueOf(goodsSku.getWeight() * goods.getGoodsNum()));
                    }
                }
            } catch (Exception e) {
                log.error("计算商品重量出错: " + e.getMessage());
            }
        }
        detailVo.setGoodsWeight(totalWeight);
        
        // 设置配送费
        detailVo.setDeliveryFee(order.getExpressMoney());
        
        // 设置活动优惠
        BigDecimal activityDiscount = BigDecimal.ZERO;
        // 从优惠券金额获取活动优惠
        if (order.getCouponMoney() != null) {
            activityDiscount = order.getCouponMoney();
        }
        detailVo.setActivityDiscount(activityDiscount);
        
        // 设置优惠券信息
        if (order.getCouponMoney() != null && order.getCouponMoney().compareTo(BigDecimal.ZERO) > 0) {
            detailVo.setCouponInfo("已使用￥" + order.getCouponMoney());
        } else {
            detailVo.setCouponInfo("无可用");
        }
        
        // 设置积分抵扣
        // 当前系统中Order实体没有积分抵扣相关字段
        detailVo.setIntegralInfo("无可用");
        
        // 设置下单时间
        detailVo.setCreateTime(order.getCreateTime());
        
        // 设置支付方式
        String payWay = "未支付";
        if (order.getPayIs() == 1) {
            payWay = com.mdd.common.enums.PaymentEnum.getPayWayMsg(order.getPayWay());
        }
        detailVo.setPayWay(payWay);
        
        return detailVo;
    }

    /**
     * 礼物订单列表
     *
     * @param pageValidate 分页参数
     * @param status 订单状态
     * @return 礼物订单列表
     */
    @Override
    public PageResult<GiftListVo> list(PageValidate pageValidate, Integer status) {
        Integer userId = LikeFrontThreadLocal.getUserId();
        
        // 构建查询条件
        QueryWrapper<GiftOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        queryWrapper.eq("from_user_id", userId);
        
        // 根据状态筛选
        if (status > 0) {
            queryWrapper.eq("gift_status", status);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc("create_time");
        
        // 分页查询
        IPage<GiftOrder> iPage = giftOrderMapper.selectPage(
                new Page<>(pageValidate.getPageNo(), pageValidate.getPageSize()),
                queryWrapper);
        
        // 转换为列表结果
        List<GiftListVo> list = new ArrayList<>();
        for (GiftOrder giftOrder : iPage.getRecords()) {
            GiftListVo vo = new GiftListVo();
            vo.setId(giftOrder.getId());
            vo.setOrderId(giftOrder.getOrderId());
            vo.setOrderSn(giftOrder.getOrderSn());
            vo.setFromUserId(giftOrder.getFromUserId());
            vo.setToUserId(giftOrder.getToUserId());
            vo.setGiftStatus(giftOrder.getGiftStatus());
            vo.setGiftStatusMsg(OrderEnum.getGiftStatusMsg(giftOrder.getGiftStatus()));
            vo.setCreateTime(giftOrder.getCreateTime());
            
            // 查询订单信息
            Order order = orderMapper.selectById(giftOrder.getOrderId());
            if (order != null) {
                vo.setOrderAmount(order.getNeedPayMoney());
                vo.setGoodsMoney(order.getGoodsMoney());
                vo.setPayStatus(order.getPayIs());
            }
            
            // 查询订单商品
            List<OrderGoods> orderGoodsList = orderGoodsMapper.selectList(
                    new QueryWrapper<OrderGoods>()
                            .eq("order_id", giftOrder.getOrderId()));
            
            // 处理商品图片URL，转为绝对路径
            for (OrderGoods goods : orderGoodsList) {
                goods.setGoodsImage(UrlUtils.toAbsoluteUrl(goods.getGoodsImage()));
            }
            
            vo.setGoodsList(orderGoodsList);
            
            list.add(vo);
        }
        
        // 返回分页结果
        PageResult<GiftListVo> pageResult = new PageResult<>();
        pageResult.setCount(iPage.getTotal());
        pageResult.setLists(list);
        return pageResult;
    }

    /**
     * 领取礼物
     *
     * @param userId 用户ID
     * @param receiveValidate 领取参数
     */
    @Override
    @Transactional
    public void receiveGift(Integer userId, GiftReceiveValidate receiveValidate) {
        // 查询赠送订单
        GiftOrder giftOrder = giftOrderMapper.selectById(receiveValidate.getGiftId());
        if (giftOrder == null || giftOrder.getIsDelete() == 1) {
            throw new OperateException("赠送订单不存在");
        }
        
        // 验证不能领取自己赠送的礼物
        if (userId.equals(giftOrder.getFromUserId())) {
            throw new OperateException("不能领取自己赠送的礼物");
        }
        
        // 验证安全口令
        if (!giftOrder.getSecurityCode().equals(receiveValidate.getSecurityCode())) {
            throw new OperateException("安全口令不正确");
        }
        
        // 验证订单状态
        if (giftOrder.getGiftStatus() != OrderEnum.GIFT_STATUS_WAITING.getCode()) {
            if (giftOrder.getGiftStatus() == OrderEnum.GIFT_STATUS_RECEIVED.getCode()) {
                throw new OperateException("礼物已被领取");
            } else if (giftOrder.getGiftStatus() == OrderEnum.GIFT_STATUS_EXPIRED.getCode()) {
                throw new OperateException("礼物已过期");
            }
            throw new OperateException("礼物无法领取");
        }
        
        // 验证是否过期
        Long currentTime = System.currentTimeMillis() / 1000;
        if (giftOrder.getExpireTime() < currentTime) {
            // 更新为已过期
            giftOrder.setGiftStatus(OrderEnum.GIFT_STATUS_EXPIRED.getCode());
            giftOrder.setUpdateTime(currentTime);
            giftOrderMapper.updateById(giftOrder);
            throw new OperateException("礼物已过期");
        }
        
        // 查询原始订单
        Order order = orderMapper.selectById(giftOrder.getOrderId());
        if (order == null || order.getIsDelete() == 1) {
            throw new OperateException("订单不存在");
        }
        
        // 验证订单是否已支付
        if (order.getPayIs() != 1) {
            throw new OperateException("订单尚未支付");
        }
        
        // 保存收货地址信息
        // 如果传入了addressId，则使用现有地址
        UserAddress userAddress;
        if (receiveValidate.getAddressId() != null && receiveValidate.getAddressId() > 0) {
            userAddress = userAddressMapper.selectById(receiveValidate.getAddressId());
            Assert.notNull(userAddress, "收货地址不存在");
            Assert.isTrue(userAddress.getUserId().equals(userId), "收货地址不属于当前用户");
        } else {
            // 否则创建新地址
            userAddress = new UserAddress();
            userAddress.setUserId(userId);
            userAddress.setContact(receiveValidate.getContact());
            userAddress.setMobile(receiveValidate.getMobile());
            userAddress.setProvinceId(receiveValidate.getProvinceId());
            userAddress.setCityId(receiveValidate.getCityId());
            userAddress.setDistrictId(receiveValidate.getDistrictId());
            userAddress.setInfo(receiveValidate.getAddress());
            userAddress.setIsDefault(0);
            userAddress.setIsDelete(0);
            userAddress.setCreateTime(currentTime);
            userAddress.setUpdateTime(currentTime);
            userAddressMapper.insert(userAddress);
        }
        
        // 更新订单收货信息
        order.setAddress(getRegionName(userAddress.getProvinceId()) + " " + 
                         getRegionName(userAddress.getCityId()) + " " + 
                         getRegionName(userAddress.getDistrictId()));
        order.setAddressContact(userAddress.getContact());
        order.setAddressMobile(userAddress.getMobile());
        
        // 构建完整地址
        String province = getRegionName(userAddress.getProvinceId());
        String city = getRegionName(userAddress.getCityId());
        String district = getRegionName(userAddress.getDistrictId());
        String addressContent = province + city + district + userAddress.getInfo();
        order.setAddressContent(addressContent);
        order.setUpdateTime(currentTime);
        orderMapper.updateById(order);
        
        // 更新赠送订单状态
        giftOrder.setToUserId(userId);
        giftOrder.setGiftStatus(OrderEnum.GIFT_STATUS_RECEIVED.getCode());
        giftOrder.setReceiveTime(currentTime);
        giftOrder.setUpdateTime(currentTime);
        giftOrderMapper.updateById(giftOrder);
        
        // 记录订单日志
        logOrderMapper.add(order.getId(), 
                OrderLogEnum.TYPE_USER.getCode(), 
                OrderLogEnum.CHANNEL_USER_CONFIRM_ORDER.getCode(), 
                userId, 
                "用户领取赠送礼物");
    }
    
    /**
     * 生成四位数字安全口令
     *
     * @return 安全口令
     */
    private String generateSecurityCode() {
        Random random = new Random();
        int code = 1000 + random.nextInt(9000); // 生成1000-9999之间的随机数
        return String.valueOf(code);
    }
    
    /**
     * 生成赠送链接
     *
     * @param userId 用户ID
     * @param orderId 订单ID
     * @return 赠送链接
     */
    private String generateGiftLink(Integer userId, Integer orderId) {
        // 生成链接格式：/pages/gift/receive?id={giftId}
        // 实际链接由前端处理，这里只是生成一个唯一标识
        String uniqueId = ToolUtils.makeUUID();
        return "/pages/gift/receive?uid=" + userId + "&oid=" + orderId + "&uuid=" + uniqueId;
    }
    
    /**
     * 获取地区名称
     *
     * @param regionId 地区ID
     * @return 地区名称
     */
    private String getRegionName(Integer regionId) {
        // 实际实现应该查询地区数据库表
        // 简化处理，返回空字符串
        return "";
    }

    /**
     * 获取礼物支付成功详情
     *
     * @param orderId 订单ID
     * @return GiftPaySuccessVo
     */
    @Override
    public GiftPaySuccessVo getPaySuccessDetail(Integer orderId) {
        // 查询订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null || order.getIsDelete() == 1) {
            throw new OperateException("订单不存在");
        }
        
        // 查询礼物订单信息
        GiftOrder giftOrder = giftOrderMapper.selectOne(
                new QueryWrapper<GiftOrder>()
                        .eq("order_id", orderId)
                        .eq("is_delete", 0));
        
        if (giftOrder == null) {
            throw new OperateException("礼物订单不存在");
        }
        
        // 查询订单商品信息
        List<OrderGoods> orderGoodsList = orderGoodsMapper.selectList(
                new QueryWrapper<OrderGoods>()
                        .eq("order_id", orderId));
        
        // 构建商品列表数据
        List<GiftPaySuccessVo.GoodsItem> goodsItems = new ArrayList<>();
        for (OrderGoods orderGoods : orderGoodsList) {
            GiftPaySuccessVo.GoodsItem item = new GiftPaySuccessVo.GoodsItem();
            item.setGoodsId(orderGoods.getGoodsId());
            item.setGoodsName(orderGoods.getGoodsName());
            item.setImage(UrlUtils.toAbsoluteUrl(orderGoods.getGoodsImage()));
            item.setSkuValue(orderGoods.getGoodsSkuValue());
            item.setPrice(orderGoods.getGoodsPrice());
            item.setNum(orderGoods.getGoodsNum());
            goodsItems.add(item);
        }
        
        // 构建返回数据
        GiftPaySuccessVo paySuccessVo = new GiftPaySuccessVo();
        paySuccessVo.setGiftId(giftOrder.getId());
        paySuccessVo.setOrderId(order.getId());
        paySuccessVo.setOrderSn(order.getOrderSn());
        paySuccessVo.setPayStatus(order.getPayIs());
        paySuccessVo.setSecurityCode(giftOrder.getSecurityCode());
        paySuccessVo.setGiftLink(giftOrder.getGiftLink());
        paySuccessVo.setMessage(giftOrder.getMessage());
        paySuccessVo.setGoodsAmount(order.getGoodsMoney());
        paySuccessVo.setOrderAmount(order.getNeedPayMoney());
        paySuccessVo.setGoodsList(goodsItems);
        
        return paySuccessVo;
    }

    /**
     * 获取礼物订单支付信息详情
     *
     * @param orderId 订单ID
     * @return GiftOrderDetailVo
     */
    @Override
    public GiftOrderDetailVo getOrderDetail(Integer orderId) {
        // 查询订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null || order.getIsDelete() == 1) {
            throw new OperateException("订单不存在");
        }
        
        // 查询礼物订单信息
        GiftOrder giftOrder = giftOrderMapper.selectOne(
                new QueryWrapper<GiftOrder>()
                        .eq("order_id", orderId)
                        .eq("is_delete", 0));
        
        if (giftOrder == null) {
            throw new OperateException("礼物订单不存在");
        }
        
        // 查询订单商品
        List<OrderGoods> orderGoodsList = orderGoodsMapper.selectList(
                new QueryWrapper<OrderGoods>()
                        .eq("order_id", orderId));
        
        // 处理商品图片URL，转为绝对路径
        for (OrderGoods goods : orderGoodsList) {
            goods.setGoodsImage(UrlUtils.toAbsoluteUrl(goods.getGoodsImage()));
        }
        
        // 构建礼物订单详情
        GiftOrderDetailVo detailVo = new GiftOrderDetailVo();
        detailVo.setOrderId(order.getId());
        detailVo.setOrderSn(order.getOrderSn());
        detailVo.setGoodsList(orderGoodsList);
        detailVo.setGoodsMoney(order.getGoodsMoney());
        detailVo.setOrderAmount(order.getNeedPayMoney());
        detailVo.setCreateTime(order.getCreateTime());
        
        // 设置礼物状态和状态描述
        detailVo.setGiftStatus(giftOrder.getGiftStatus());
        detailVo.setGiftStatusMsg(OrderEnum.getGiftStatusMsg(giftOrder.getGiftStatus()));
        
        // 获取当前登录用户ID
        Integer currentUserId = LikeFrontThreadLocal.getUserId();
        Integer userRole = 0;
        
        // 判断当前用户是赠送人还是接收人
        if (currentUserId != null) {
            if (currentUserId.equals(giftOrder.getFromUserId())) {
                userRole = 1; // 赠送人
                detailVo.setUserRole(userRole);
            } else if (currentUserId.equals(giftOrder.getToUserId())) {
                userRole = 2; // 接收人
                detailVo.setUserRole(userRole);
            } else {
                detailVo.setUserRole(0); // 其他人/未知
            }
        }
        
        // 判断礼物是否已被领取
        boolean isReceived = giftOrder.getGiftStatus() != null && 
                            giftOrder.getGiftStatus() == OrderEnum.GIFT_STATUS_RECEIVED.getCode();
        
        // 根据用户角色和礼物状态设置用户信息
        if (isReceived) {
            if (userRole == 1) { // 当前用户是赠送人，显示接收人信息
                if (giftOrder.getToUserId() != null) {
                    detailVo.setToUserId(giftOrder.getToUserId());
                    User toUser = userMapper.selectById(giftOrder.getToUserId());
                    if (toUser != null) {
                        detailVo.setToUserName(toUser.getNickname());
                    }
                }
            } else if (userRole == 2) { // 当前用户是接收人，显示赠送人信息
                detailVo.setFromUserId(giftOrder.getFromUserId());
                User fromUser = userMapper.selectById(giftOrder.getFromUserId());
                if (fromUser != null) {
                    detailVo.setFromUserName(fromUser.getNickname());
                }
            }
        }
        
        // 设置配送费
        detailVo.setDeliveryFee(order.getExpressMoney());
        
        // 设置活动优惠
        BigDecimal activityDiscount = BigDecimal.ZERO;
        // 从优惠券金额获取活动优惠
        if (order.getCouponMoney() != null) {
            activityDiscount = order.getCouponMoney();
        }
        detailVo.setActivityDiscount(activityDiscount);
        
        // 设置优惠券信息
        if (order.getCouponMoney() != null && order.getCouponMoney().compareTo(BigDecimal.ZERO) > 0) {
            detailVo.setCouponInfo("已使用￥" + order.getCouponMoney());
        } else {
            detailVo.setCouponInfo("无可用");
        }
        
        // 设置积分抵扣
        // 当前系统中Order实体没有积分抵扣相关字段
        detailVo.setIntegralInfo("无可用");
        
        // 设置支付方式，使用PaymentEnum处理
        String payWay = "未支付";
        if (order.getPayIs() == 1) {
            payWay = com.mdd.common.enums.PaymentEnum.getPayWayMsg(order.getPayWay());
        }
        detailVo.setPayWay(payWay);
        
        return detailVo;
    }

    /**
     * 验证礼物订单
     *
     * @param orderId 订单ID
     * @return 验证结果
     */
    @Override
    public Boolean checkGiftOrderByOrderId(Integer orderId) {
        // 查询订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null || order.getIsDelete() == 1) {
            throw new OperateException("订单不存在");
        }
        
        // 查询礼物订单信息
        GiftOrder giftOrder = giftOrderMapper.selectOne(
                new QueryWrapper<GiftOrder>()
                        .eq("order_id", orderId)
                        .eq("is_delete", 0));
        
        // 如果不存在礼物订单记录，说明不是礼物订单
        if (giftOrder == null) {
            return false;
        }
        
        // 检查订单是否已支付
        if (order.getPayIs() != 1) {
            throw new OperateException("礼物订单尚未支付");
        }
        
        // 验证订单状态
        if (giftOrder.getGiftStatus() == OrderEnum.GIFT_STATUS_RECEIVED.getCode()) {
            throw new OperateException("礼物已被领取");
        }
        
        // 验证是否过期
        Long currentTime = System.currentTimeMillis() / 1000;
        if (giftOrder.getExpireTime() < currentTime) {
            // 如果已过期但状态未更新，则更新状态
            if (giftOrder.getGiftStatus() != OrderEnum.GIFT_STATUS_EXPIRED.getCode()) {
                giftOrder.setGiftStatus(OrderEnum.GIFT_STATUS_EXPIRED.getCode());
                giftOrder.setUpdateTime(currentTime);
                giftOrderMapper.updateById(giftOrder);
            }
            throw new OperateException("礼物已过期");
        }
        
        // 验证礼物状态
        if (giftOrder.getGiftStatus() != OrderEnum.GIFT_STATUS_WAITING.getCode()) {
            throw new OperateException("礼物无法领取");
        }
        
        // 验证成功，可以领取
        return true;
    }

    /**
     * 验证礼物安全口令
     *
     * @param validate 安全口令验证参数
     * @return 验证结果
     */
    @Override
    public GiftSecurityCodeVo validateSecurityCode(GiftReceiveByCodeValidate validate) {
        GiftSecurityCodeVo resultVo = new GiftSecurityCodeVo();
        resultVo.setIsValid(false);
        
        try {
            // 获取当前登录用户ID
            Integer currentUserId = LikeFrontThreadLocal.getUserId();
            
            // 根据订单ID查询礼物订单
            GiftOrder giftOrder = giftOrderMapper.selectOne(
                    new QueryWrapper<GiftOrder>()
                            .eq("order_id", validate.getOrderId())
                            .eq("is_delete", 0)
                            .last("limit 1"));
            
            // 验证礼物订单存在
            if (giftOrder == null) {
                throw new OperateException("礼物订单不存在");
            }
            
            // 验证不能领取自己赠送的礼物
            if (currentUserId.equals(giftOrder.getFromUserId())) {
                throw new OperateException("不能领取自己赠送的礼物");
            }
            
            // 验证安全口令
            if (!giftOrder.getSecurityCode().equals(validate.getSecurityCode())) {
                throw new OperateException("安全口令不正确");
            }
            
            // 验证礼物状态
            if (giftOrder.getGiftStatus() != OrderEnum.GIFT_STATUS_WAITING.getCode()) {
                if (giftOrder.getGiftStatus() == OrderEnum.GIFT_STATUS_RECEIVED.getCode()) {
                    throw new OperateException("礼物已被领取");
                } else if (giftOrder.getGiftStatus() == OrderEnum.GIFT_STATUS_EXPIRED.getCode()) {
                    throw new OperateException("礼物已过期");
                }
                throw new OperateException("礼物无法领取");
            }
            
            // 验证是否过期
            Long currentTime = System.currentTimeMillis() / 1000;
            if (giftOrder.getExpireTime() < currentTime) {
                // 更新为已过期
                giftOrder.setGiftStatus(OrderEnum.GIFT_STATUS_EXPIRED.getCode());
                giftOrder.setUpdateTime(currentTime);
                giftOrderMapper.updateById(giftOrder);
                throw new OperateException("礼物已过期");
            }
            
            // 查询原始订单
            Order order = orderMapper.selectById(giftOrder.getOrderId());
            if (order == null || order.getIsDelete() == 1) {
                throw new OperateException("订单不存在");
            }
            
            // 验证订单是否已支付
            if (order.getPayIs() != 1) {
                throw new OperateException("订单尚未支付");
            }
            
            // 查询订单商品，获取商品名称
            List<OrderGoods> orderGoodsList = orderGoodsMapper.selectList(
                    new QueryWrapper<OrderGoods>()
                            .eq("order_id", validate.getOrderId())
                            .last("limit 1"));
            
            String goodsName = "";
            String goodsImage = "";
            Integer goodsNum = 0;
            if (!orderGoodsList.isEmpty()) {
                goodsName = orderGoodsList.get(0).getGoodsName();
                goodsImage = orderGoodsList.get(0).getGoodsImage();
                goodsNum = orderGoodsList.get(0).getGoodsNum();
            }
            
            // 查询发送者用户信息
            User fromUser = userMapper.selectById(giftOrder.getFromUserId());
            String fromUserNickname = "";
            String fromUserAvatar = "";
            if (fromUser != null) {
                fromUserNickname = fromUser.getNickname();
                fromUserAvatar = UrlUtils.toAbsoluteUrl(fromUser.getAvatar());
            }
            
            // 填充返回结果
            resultVo.setIsValid(true);
            resultVo.setGiftId(giftOrder.getId());
            resultVo.setOrderId(giftOrder.getOrderId());
            resultVo.setGoodsName(goodsName);
            resultVo.setGoodsImage(UrlUtils.toAbsoluteUrl(goodsImage));
            resultVo.setGoodsNum(goodsNum);
            resultVo.setFromUserId(giftOrder.getFromUserId());
            resultVo.setFromUserNickname(fromUserNickname);
            resultVo.setFromUserAvatar(fromUserAvatar);
            resultVo.setSecurityCode(giftOrder.getSecurityCode());
            resultVo.setMessage(giftOrder.getMessage());
            
            return resultVo;
        } catch (OperateException e) {
            throw e;
        } catch (Exception e) {
            throw new OperateException("验证礼物安全口令失败: " + e.getMessage());
        }
    }

    /**
     * 确认接收礼物
     *
     * @param userId 用户ID
     * @param validate 礼物确认接收参数
     * @return 接收结果
     */
    @Override
    @Transactional
    public boolean confirmReceiveGift(Integer userId, GiftConfirmReceiveValidate validate) {
        // 根据礼物ID查询礼物订单
        GiftOrder giftOrder = giftOrderMapper.selectById(validate.getGiftId());
        
        // 验证礼物订单存在
        if (giftOrder == null || giftOrder.getIsDelete() == 1) {
            throw new OperateException("礼物订单不存在");
        }
        
        // 验证不能领取自己赠送的礼物
        if (userId.equals(giftOrder.getFromUserId())) {
            throw new OperateException("不能领取自己赠送的礼物");
        }
        
        // 验证礼物状态
        if (giftOrder.getGiftStatus() != OrderEnum.GIFT_STATUS_WAITING.getCode()) {
            if (giftOrder.getGiftStatus() == OrderEnum.GIFT_STATUS_RECEIVED.getCode()) {
                throw new OperateException("礼物已被领取");
            } else if (giftOrder.getGiftStatus() == OrderEnum.GIFT_STATUS_EXPIRED.getCode()) {
                throw new OperateException("礼物已过期");
            }
            throw new OperateException("礼物无法领取");
        }
        
        // 验证是否过期
        Long currentTime = System.currentTimeMillis() / 1000;
        if (giftOrder.getExpireTime() < currentTime) {
            // 更新为已过期
            giftOrder.setGiftStatus(OrderEnum.GIFT_STATUS_EXPIRED.getCode());
            giftOrder.setUpdateTime(currentTime);
            giftOrderMapper.updateById(giftOrder);
            throw new OperateException("礼物已过期");
        }
        
        // 查询原始订单
        Order order = orderMapper.selectById(giftOrder.getOrderId());
        if (order == null || order.getIsDelete() == 1) {
            throw new OperateException("订单不存在");
        }
        
        // 验证订单是否已支付
        if (order.getPayIs() != 1) {
            throw new OperateException("订单尚未支付");
        }
        
        // 如果提供了地址ID，则修改订单地址信息
        if (validate.getAddressId() != null) {
            // 根据地址ID查询地址信息
            UserAddress userAddress = userAddressMapper.selectById(validate.getAddressId());
            if (userAddress == null || userAddress.getIsDelete() == 1) {
                throw new OperateException("地址不存在");
            }
            
            // 验证地址是否属于当前用户
            if (!userAddress.getUserId().equals(userId)) {
                throw new OperateException("无权限使用此地址");
            }
            
            // 更新订单地址信息
            order.setAddress(getRegionName(userAddress.getProvinceId()) + " " + 
                             getRegionName(userAddress.getCityId()) + " " + 
                             getRegionName(userAddress.getDistrictId()));
            order.setAddressContact(userAddress.getContact());
            order.setAddressMobile(userAddress.getMobile());
            order.setAddressContent(userAddress.getInfo());
            order.setUpdateTime(currentTime);
            orderMapper.updateById(order);
            

        }
        
        // 更新礼物订单状态
        giftOrder.setToUserId(userId);
        giftOrder.setGiftStatus(OrderEnum.GIFT_STATUS_RECEIVED.getCode());
        giftOrder.setReceiveTime(currentTime);
        giftOrder.setUpdateTime(currentTime);
        giftOrderMapper.updateById(giftOrder);
        
        // 记录订单日志
        logOrderMapper.add(
                giftOrder.getOrderId(),
                OrderLogEnum.TYPE_USER.getCode(),
                OrderLogEnum.CHANNEL_USER_CONFIRM_ORDER.getCode(),
                userId,
                "用户确认接收礼物"
        );
        
        return true;
    }

    /**
     * 查询不同类型的礼物订单
     *
     * @param pageValidate 分页参数
     * @param type 查询类型 [1=送的人是自己且接收人不为空, 2=接收人是自己, 3=送的人是自己且接收人为空]
     * @param status 订单状态
     * @return 礼物订单列表
     */
    @Override
    public PageResult<GiftQueryVo> queryList(PageValidate pageValidate, Integer type, Integer status) {
        Integer userId = LikeFrontThreadLocal.getUserId();
        List<GiftQueryVo> resultList = new ArrayList<>();
        long totalCount = 0;

        // 如果type为null，则查询所有类型的礼物订单
        if (type == null) {
            return queryAllTypesGift(pageValidate, status, userId);
        }

        switch (type) {
            case 1: // 送的人是自己且接收人不为空（已被领取的礼物）
                totalCount = queryGiftSent(pageValidate, status, userId, resultList);
                break;
            case 2: // 接收人是自己（收到的礼物）
                totalCount = queryGiftReceived(pageValidate, status, userId, resultList);
                break;
            case 3: // 送的人是自己且接收人为空（等待被领取的礼物）
                totalCount = queryGiftWaiting(pageValidate, status, userId, resultList);
                break;
            default:
                throw new OperateException("不支持的查询类型");
        }

        // 构建分页结果
        PageResult<GiftQueryVo> pageResult = new PageResult<>();
        pageResult.setCount(totalCount);
        pageResult.setLists(resultList);
        return pageResult;
    }

    /**
     * 查询我赠送的且已被领取的礼物订单（送的人是自己且接收人不为空）
     */
    private long queryGiftSent(PageValidate pageValidate, Integer status, Integer userId, List<GiftQueryVo> resultList) {
        // 构建查询条件
        QueryWrapper<GiftOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        queryWrapper.eq("from_user_id", userId);
        queryWrapper.isNotNull("to_user_id");  // 接收人不为空，表示已被领取
        
        // 根据状态筛选
        if (status > 0) {
            queryWrapper.eq("gift_status", status);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc("create_time");
        
        // 分页查询
        IPage<GiftOrder> iPage = giftOrderMapper.selectPage(
                new Page<>(pageValidate.getPageNo(), pageValidate.getPageSize()),
                queryWrapper);
        
        // 处理查询结果
        for (GiftOrder giftOrder : iPage.getRecords()) {
            GiftQueryVo vo = new GiftQueryVo();
            vo.setGiftId(giftOrder.getId());
            vo.setOrderId(giftOrder.getOrderId());
            vo.setOrderSn(giftOrder.getOrderSn());
            vo.setOrderType(1); // 我赠送的且已被领取
            vo.setFromUserId(giftOrder.getFromUserId());
            vo.setToUserId(giftOrder.getToUserId());
            vo.setGiftStatus(giftOrder.getGiftStatus());
            vo.setGiftStatusMsg(OrderEnum.getGiftStatusMsg(giftOrder.getGiftStatus()));
            vo.setGiftLink(giftOrder.getGiftLink());
            vo.setSecurityCode(giftOrder.getSecurityCode());
            vo.setMessage(giftOrder.getMessage());
            vo.setCreateTime(giftOrder.getCreateTime());
            
            // 查询用户信息
            enrichUserInfo(vo);
            
            // 查询订单信息
            enrichOrderInfo(vo);
            
            // 查询商品信息
            enrichGoodsInfo(vo);
            
            resultList.add(vo);
        }
        
        return iPage.getTotal();
    }

    /**
     * 查询我收到的礼物订单（接收人是自己）
     */
    private long queryGiftReceived(PageValidate pageValidate, Integer status, Integer userId, List<GiftQueryVo> resultList) {
        // 构建查询条件
        QueryWrapper<GiftOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        queryWrapper.eq("to_user_id", userId);
        
        // 根据状态筛选
        if (status > 0) {
            queryWrapper.eq("gift_status", status);
        } else {
            // 默认只查询已领取的礼物
            queryWrapper.eq("gift_status", OrderEnum.GIFT_STATUS_RECEIVED.getCode());
        }
        
        // 按领取时间倒序排序
        queryWrapper.orderByDesc("receive_time");
        
        // 分页查询
        IPage<GiftOrder> iPage = giftOrderMapper.selectPage(
                new Page<>(pageValidate.getPageNo(), pageValidate.getPageSize()),
                queryWrapper);
        
        // 处理查询结果
        for (GiftOrder giftOrder : iPage.getRecords()) {
            GiftQueryVo vo = new GiftQueryVo();
            vo.setGiftId(giftOrder.getId());
            vo.setOrderId(giftOrder.getOrderId());
            vo.setOrderSn(giftOrder.getOrderSn());
            vo.setOrderType(2); // 我收到的
            vo.setFromUserId(giftOrder.getFromUserId());
            vo.setToUserId(giftOrder.getToUserId());
            vo.setGiftStatus(giftOrder.getGiftStatus());
            vo.setGiftStatusMsg(OrderEnum.getGiftStatusMsg(giftOrder.getGiftStatus()));
            vo.setMessage(giftOrder.getMessage());
            vo.setCreateTime(giftOrder.getCreateTime());
            
            // 查询用户信息
            enrichUserInfo(vo);
            
            // 查询订单信息
            enrichOrderInfo(vo);
            
            // 查询商品信息
            enrichGoodsInfo(vo);
            
            resultList.add(vo);
        }
        
        return iPage.getTotal();
    }
    
    /**
     * 查询我送出但未被领取的礼物订单（送的人是自己且接收人为空）
     */
    private long queryGiftWaiting(PageValidate pageValidate, Integer status, Integer userId, List<GiftQueryVo> resultList) {
        // 构建查询条件
        QueryWrapper<GiftOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        queryWrapper.eq("from_user_id", userId);
        queryWrapper.isNull("to_user_id");  // 接收人为空，表示尚未被领取
        
        // 根据状态筛选
        if (status > 0) {
            queryWrapper.eq("gift_status", status);
        } else {
            // 默认只查询等待领取的礼物
            queryWrapper.eq("gift_status", OrderEnum.GIFT_STATUS_WAITING.getCode());
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc("create_time");
        
        // 分页查询
        IPage<GiftOrder> iPage = giftOrderMapper.selectPage(
                new Page<>(pageValidate.getPageNo(), pageValidate.getPageSize()),
                queryWrapper);
        
        // 处理查询结果
        for (GiftOrder giftOrder : iPage.getRecords()) {
            GiftQueryVo vo = new GiftQueryVo();
            vo.setGiftId(giftOrder.getId());
            vo.setOrderId(giftOrder.getOrderId());
            vo.setOrderSn(giftOrder.getOrderSn());
            vo.setOrderType(3); // 我送出但未被领取的
            vo.setFromUserId(giftOrder.getFromUserId());
            vo.setToUserId(giftOrder.getToUserId()); // 这里应该为null
            vo.setGiftStatus(giftOrder.getGiftStatus());
            vo.setGiftStatusMsg(OrderEnum.getGiftStatusMsg(giftOrder.getGiftStatus()));
            vo.setGiftLink(giftOrder.getGiftLink());
            vo.setSecurityCode(giftOrder.getSecurityCode());
            vo.setMessage(giftOrder.getMessage());
            vo.setCreateTime(giftOrder.getCreateTime());
            
            // 查询用户信息
            enrichUserInfo(vo);
            
            // 查询订单信息
            enrichOrderInfo(vo);
            
            // 查询商品信息
            enrichGoodsInfo(vo);
            
            resultList.add(vo);
        }
        
        return iPage.getTotal();
    }

    /**
     * 填充用户信息
     */
    private void enrichUserInfo(GiftQueryVo vo) {
        // 赠送者信息
        if (vo.getFromUserId() != null) {
            User fromUser = userMapper.selectById(vo.getFromUserId());
            if (fromUser != null) {
                vo.setFromUserNickname(fromUser.getNickname());
                vo.setFromUserAvatar(UrlUtils.toAbsoluteUrl(fromUser.getAvatar()));
            }
        }
        
        // 接收者信息
        if (vo.getToUserId() != null) {
            User toUser = userMapper.selectById(vo.getToUserId());
            if (toUser != null) {
                vo.setToUserNickname(toUser.getNickname());
                vo.setToUserAvatar(UrlUtils.toAbsoluteUrl(toUser.getAvatar()));
            }
        }
    }

    /**
     * 填充订单信息
     */
    private void enrichOrderInfo(GiftQueryVo vo) {
        Order order = orderMapper.selectById(vo.getOrderId());
        if (order != null) {
            vo.setOrderAmount(order.getNeedPayMoney());
            vo.setGoodsMoney(order.getGoodsMoney());
            vo.setPayStatus(order.getPayIs());
            vo.setOrderStatus(order.getOrderStatus());
            vo.setOrderStatusMsg(OrderEnum.getOrderStatusMsg(order.getOrderStatus()));
        }
    }

    /**
     * 填充商品信息
     */
    private void enrichGoodsInfo(GiftQueryVo vo) {
        List<OrderGoods> orderGoodsList = orderGoodsMapper.selectList(
                new QueryWrapper<OrderGoods>()
                        .eq("order_id", vo.getOrderId()));
        
        // 处理商品图片URL，转为绝对路径
        for (OrderGoods goods : orderGoodsList) {
            goods.setGoodsImage(UrlUtils.toAbsoluteUrl(goods.getGoodsImage()));
        }
        
        vo.setGoodsList(orderGoodsList);
    }

    /**
     * 查询所有类型的礼物订单
     * 
     * @param pageValidate 分页参数
     * @param status 订单状态
     * @param userId 用户ID
     * @return 综合的礼物订单列表
     */
    private PageResult<GiftQueryVo> queryAllTypesGift(PageValidate pageValidate, Integer status, Integer userId) {
        // 计算每种类型分配的分页大小（平均分配）
        int pageSize = pageValidate.getPageSize();
        int pageNo = pageValidate.getPageNo();
        int offset = (pageNo - 1) * pageSize; // 计算当前页的起始偏移量
        
        // 先获取每种类型的总数据量，用于后续分页计算
        List<GiftQueryVo> allResults = new ArrayList<>();

        // 临时分页参数（查询足够多的数据以便合并后进行内存分页）
        // 这里使用较大的页码1和较大的每页大小，确保能获取足够的数据
        PageValidate tempPageValidate = new PageValidate();
        tempPageValidate.setPageNo(1);
        // 设置一个足够大的页大小，确保能获取到所有符合条件的数据
        // 实际项目中可以根据性能情况调整
        tempPageValidate.setPageSize(1000);
        
        // 查询三种类型的数据
        List<GiftQueryVo> sentList = new ArrayList<>();
        List<GiftQueryVo> receivedList = new ArrayList<>();
        List<GiftQueryVo> waitingList = new ArrayList<>();
        
        // 查询已赠送的礼物订单（送的人是自己且接收人不为空）
        long sentCount = queryGiftSent(tempPageValidate, status, userId, sentList);
        
        // 查询我收到的礼物订单（接收人是自己）
        long receivedCount = queryGiftReceived(tempPageValidate, status, userId, receivedList);
        
        // 查询待领取的礼物订单（送的人是自己且接收人为空）
        long waitingCount = queryGiftWaiting(tempPageValidate, status, userId, waitingList);
        
        // 合并所有结果
        allResults.addAll(sentList);
        allResults.addAll(receivedList);
        allResults.addAll(waitingList);
        
        // 按创建时间降序排序
        allResults.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));
        
        // 计算总数（使用合并后的实际大小）
        long totalCount = allResults.size();
        
        // 从合并后的结果中取出对应分页的数据
        List<GiftQueryVo> pageResults = new ArrayList<>();
        int endIndex = Math.min(offset + pageSize, allResults.size());
        
        // 确保索引不越界
        if (offset < allResults.size()) {
            pageResults = allResults.subList(offset, endIndex);
        }
        
        // 构建分页结果
        PageResult<GiftQueryVo> pageResult = new PageResult<>();
        pageResult.setCount(totalCount);
        pageResult.setLists(pageResults);
        
        return pageResult;
    }
} 