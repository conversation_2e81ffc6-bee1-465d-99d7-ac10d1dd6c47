package com.mdd.front.service.beauty.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mdd.common.entity.beauty.BeautyBanner;
import com.mdd.common.mapper.beauty.BeautyBannerMapper;
import com.mdd.common.util.UrlUtils;
import com.mdd.front.service.beauty.IBeautyBannerService;
import com.mdd.front.vo.beauty.BeautyBannerVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 美妆社区轮播图服务实现类
 */
@Service
public class BeautyBannerServiceImpl implements IBeautyBannerService {

    @Resource
    private BeautyBannerMapper beautyBannerMapper;

    /**
     * 获取前台轮播图列表
     */
    @Override
    public List<BeautyBannerVo> frontList() {
        QueryWrapper<BeautyBanner> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("del", 0);
        queryWrapper.orderByAsc("sort");
        
        List<BeautyBanner> banners = beautyBannerMapper.selectList(queryWrapper);
        
        List<BeautyBannerVo> list = new ArrayList<>();
        for (BeautyBanner banner : banners) {
            BeautyBannerVo vo = new BeautyBannerVo();
            BeanUtils.copyProperties(banner, vo);
            // 转换图片路径为绝对路径
            vo.setImage(UrlUtils.toAbsoluteUrl(banner.getImage()));
            list.add(vo);
        }
        
        return list;
    }
} 