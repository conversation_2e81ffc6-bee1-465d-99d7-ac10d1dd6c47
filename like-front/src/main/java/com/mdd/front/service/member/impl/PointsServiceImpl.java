package com.mdd.front.service.member.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mdd.common.entity.member.MemberPoint;
import com.mdd.common.entity.member.MemberPointLog;
import com.mdd.common.mapper.member.MemberPointLogMapper;
import com.mdd.common.mapper.member.MemberPointMapper;
import com.mdd.common.util.TimeUtils;
import com.mdd.front.service.member.IPointsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 积分服务实现类
 */
@Service
public class PointsServiceImpl implements IPointsService {

    @Resource
    private MemberPointMapper memberPointMapper;

    @Resource
    private MemberPointLogMapper memberPointLogMapper;

    /**
     * 获取用户积分信息
     *
     * @param userId 用户ID
     * @return Map<String, Object> 用户积分信息
     */
    @Override
    public Map<String, Object> getUserPoints(Integer userId) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查用户ID是否有效
        if (userId == null || userId <= 0) {
            result.put("total", 0);
            result.put("consume", 0);
            result.put("family", 0);
            result.put("activity", 0);
            return result;
        }
        
        // 查询该用户的会员积分信息
        QueryWrapper<MemberPoint> pointQueryWrapper = new QueryWrapper<>();
        pointQueryWrapper.eq("user_id", userId)
                .eq("is_delete", 0);
        MemberPoint memberPoint = memberPointMapper.selectOne(pointQueryWrapper);
        
        // 默认总积分值
        int totalPoints = 0;
        if (memberPoint != null) {
            totalPoints = memberPoint.getAvailablePoint();
        }
        
        // 查询各类型积分汇总
        // 消费积分：消费得积分+充值得积分 (类型6和7)
        int consumePoints = getSumPointsByTypes(userId, Arrays.asList(6, 7));
        
        // 推广积分：社区点赞+社区评论+社区转发+社区发帖 (类型1,2,3,4)
        int familyPoints = getSumPointsByTypes(userId, Arrays.asList(1, 2, 3, 4));
        
        // 扫码积分：扫码得积分 (类型5)
        int activityPoints = getSumPointsByTypes(userId, Arrays.asList(5));
        
        result.put("total", totalPoints);
        result.put("consume", consumePoints);
        result.put("family", familyPoints);
        result.put("activity", activityPoints);
        
        return result;
    }
    
    /**
     * 获取积分明细
     *
     * @param userId 用户ID
     * @param page 页码
     * @param limit 每页条数
     * @param type 类型 0-全部 1-收益明细 2-兑换明细
     * @return Map<String, Object> 积分明细及分页信息
     */
    @Override
    public Map<String, Object> getPointsDetail(Integer userId, Integer page, Integer limit, Integer type) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查用户ID是否有效
        if (userId == null || userId <= 0) {
            result.put("list", new ArrayList<>());
            result.put("total", 0);
            return result;
        }
        
        // 查询条件
        QueryWrapper<MemberPointLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("is_delete", 0);
        
        // 根据类型筛选
        if (type == 1) {
            // 收益明细（积分为正）
            queryWrapper.gt("point", 0);
        } else if (type == 2) {
            // 兑换明细（积分为负）
            queryWrapper.lt("point", 0);
        }
        
        // 按时间降序排序
        queryWrapper.orderByDesc("create_time");
        
        // 分页查询
        Page<MemberPointLog> pageData = new Page<>(page, limit);
        Page<MemberPointLog> pageResult = memberPointLogMapper.selectPage(pageData, queryWrapper);
        
        // 构建结果集
        List<Map<String, Object>> records = new ArrayList<>();
        for (MemberPointLog log : pageResult.getRecords()) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", log.getId());
            item.put("title", formatPointDetail(log.getType(), log.getDescription()));
            item.put("time", TimeUtils.timestampToDate(log.getCreateTime()));
            item.put("points", log.getPoint());
            item.put("type", log.getType());
            records.add(item);
        }
        
        result.put("list", records);
        result.put("total", pageResult.getTotal());
        
        return result;
    }
    
    /**
     * 获取积分规则
     *
     * @return Map<String, Object> 积分规则
     */
    @Override
    public Map<String, Object> getPointsRules() {
        Map<String, Object> result = new HashMap<>();
        
        List<Map<String, Object>> rules = new ArrayList<>();
        
        Map<String, Object> rule1 = new HashMap<>();
        rule1.put("title", "顾客在绑定会员卡之前的微信小程序消费均不积分。");
        //rule1.put("content", "1. 消费获取：每消费1元累计1积分\n2. 社区互动：点赞/评论/转发/发帖各得5积分\n3. 扫码签到：每日首次扫码得10积分");
        rules.add(rule1);
        
        Map<String, Object> rule2 = new HashMap<>();
        rule2.put("title", "每个会员ID限一人使用并累积积分，积分不可转让,可用于小程序内换购。");
        //rule2.put("content", "1. 积分可在小程序内兑换指定商品\n2. 积分兑换比例：10积分=1元\n3. 积分不可提现，仅限兑换使用");
        rules.add(rule2);
        
        Map<String, Object> rule3 = new HashMap<>();
        rule3.put("title", "会员在微信小程序下单后，将在确认收货后获得积分。");
        //rule3.put("content", "所有积分自获取之日起两年内有效，过期将自动失效");
        rules.add(rule3);

        Map<String, Object> rule4 = new HashMap<>();
        rule4.put("title", "会员可在小程序换购相应商品，10积分抵扣1元。");
        //rule4.put("content", "兑换商品后，积分将自动扣除，且不可退换");
        rules.add(rule4);

        Map<String, Object> rule5 = new HashMap<>();
        rule5.put("title", "积分兑换订单如果发生退货，积分将不予恢复。");
        //rule5.put("content", "积分兑换商品后，积分将自动扣除，且不可退换");
        rules.add(rule5);
        
        result.put("rules", rules);
        
        return result;
    }
    
    /**
     * 根据积分类型获取积分总和
     *
     * @param userId 用户ID
     * @param types 积分类型列表
     * @return int 积分总和
     */
    private int getSumPointsByTypes(Integer userId, List<Integer> types) {
        if (types == null || types.isEmpty()) {
            return 0;
        }
        
        QueryWrapper<MemberPointLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(SUM(point), 0) as point_sum")
                   .eq("user_id", userId)
                   .eq("is_delete", 0)
                   .in("type", types);
        
        List<Map<String, Object>> resultList = memberPointLogMapper.selectMaps(queryWrapper);
        if (resultList == null || resultList.isEmpty()) {
            return 0;
        }
        
        Map<String, Object> result = resultList.get(0);
        if (result == null || !result.containsKey("point_sum")) {
            return 0;
        }
        
        Object pointSum = result.get("point_sum");
        if (pointSum == null) {
            return 0;
        }
        
        try {
            if (pointSum instanceof Number) {
                return ((Number) pointSum).intValue();
            } else {
                return Integer.parseInt(pointSum.toString());
            }
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * 格式化积分明细描述
     *
     * @param type 积分类型
     * @param description 原始描述
     * @return String 格式化后的描述
     */
    private String formatPointDetail(Integer type, String description) {
        // 如果已有描述，直接返回
        if (description != null && !description.isEmpty()) {
            return description;
        }
        
        // 根据类型获取默认描述
        switch (type) {
            case 1:
                return "社区点赞奖励";
            case 2:
                return "社区评论奖励";
            case 3:
                return "社区转发奖励";
            case 4:
                return "社区发帖奖励";
            case 5:
                return "扫码签到奖励";
            case 6:
                return "消费积分奖励";
            case 7:
                return "充值积分奖励";
            case 8:
                return "积分兑换商品";
            case 9:
                return "积分过期失效";
            case 10:
                return "管理员调整";
            default:
                return "积分变动";
        }
    }
} 