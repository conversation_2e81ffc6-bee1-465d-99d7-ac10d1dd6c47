package com.mdd.front.service.member.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.query.MPJQueryWrapper;
import com.mdd.common.entity.member.MemberBenefit;
import com.mdd.common.entity.member.MemberBenefitRecord;
import com.mdd.common.entity.member.MemberCard;
import com.mdd.common.entity.member.MemberLevel;
import com.mdd.common.entity.member.MemberPoint;
import com.mdd.common.entity.member.MemberPointLog;
import com.mdd.common.entity.user.User;
import com.mdd.common.exception.OperateException;
import com.mdd.common.mapper.member.MemberBenefitMapper;
import com.mdd.common.mapper.member.MemberBenefitRecordMapper;
import com.mdd.common.mapper.member.MemberCardMapper;
import com.mdd.common.mapper.member.MemberLevelMapper;
import com.mdd.common.mapper.member.MemberPointLogMapper;
import com.mdd.common.mapper.member.MemberPointMapper;
import com.mdd.common.mapper.user.UserMapper;
import com.mdd.common.util.TimeUtils;
import com.mdd.common.util.UrlUtils;
import com.mdd.front.service.member.IMemberService;
import com.mdd.front.validate.member.MemberRegisterValidate;
import com.mdd.common.enums.BenefitTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 会员服务实现类
 */
@Service
public class MemberServiceImpl implements IMemberService {

    @Resource
    private MemberCardMapper laMemberCardMapper;

    @Resource
    private MemberLevelMapper laMemberLevelMapper;

    @Resource
    private MemberPointMapper laMemberPointMapper;

    @Resource
    private MemberPointLogMapper laMemberPointLogMapper;
    
    @Resource
    private MemberBenefitMapper laMemberBenefitMapper;
    
    @Resource
    private MemberBenefitRecordMapper memberBenefitRecordMapper;
    
    @Resource
    private UserMapper userMapper;

    /**
     * 会员注册
     *
     * <AUTHOR> Name
     * @param registerValidate 注册参数
     */
    @Transactional
    @Override
    public void register(MemberRegisterValidate registerValidate) {
        Integer userId = registerValidate.getUserId();
        
        if (userId == null || userId <= 0) {
            throw new OperateException("用户ID无效");
        }

        // 检查用户是否已经注册为会员
        QueryWrapper<MemberCard> cardQueryWrapper = new QueryWrapper<>();
        cardQueryWrapper.eq("user_id", userId)
                .eq("is_delete", 0);
        MemberCard existCard = laMemberCardMapper.selectOne(cardQueryWrapper);
        if (existCard != null) {
            throw new OperateException("该用户已经是会员");
        }

        // 获取白银会员等级ID
        QueryWrapper<MemberLevel> levelQueryWrapper = new QueryWrapper<>();
        levelQueryWrapper.eq("level", 1)  // 1=白银会员
                .eq("status", 1)
                .eq("is_delete", 0);
        MemberLevel memberLevel = laMemberLevelMapper.selectOne(levelQueryWrapper);
        if (memberLevel == null) {
            throw new OperateException("白银会员等级不存在");
        }

        // 将long类型的时间戳转换为int
        long timestamp = TimeUtils.timestamp();

        // 创建会员卡记录
        MemberCard memberCard = new MemberCard();
        memberCard.setUserId(userId);
        memberCard.setLevel(1); // 1=白银会员
        
        // 生成会员卡号
        String cardNo = com.mdd.common.util.CardNoUtils.generateCardNo();
        memberCard.setCardNo(cardNo);
        
        memberCard.setIsActive(1); // 1=激活
        memberCard.setActiveTime(timestamp);
        memberCard.setExpireTime(0L); // 永久有效
        memberCard.setIsDelete(0);
        memberCard.setCreateTime(timestamp);
        memberCard.setUpdateTime(timestamp);
        memberCard.setDeleteTime(0L);
        laMemberCardMapper.insert(memberCard);
        
        // 初始化会员积分记录
        QueryWrapper<MemberPoint> pointQueryWrapper = new QueryWrapper<>();
        pointQueryWrapper.eq("user_id", userId)
                .eq("is_delete", 0);
        MemberPoint existPoint = laMemberPointMapper.selectOne(pointQueryWrapper);
        
        // 如果不存在积分记录，则创建一个新的
        if (existPoint == null) {
            MemberPoint memberPoint = new MemberPoint();
            memberPoint.setUserId(userId);
            memberPoint.setAvailablePoint(0);
            memberPoint.setUsedPoint(0);
            memberPoint.setExpiredPoint(0);
            memberPoint.setIsDelete(0);
            memberPoint.setCreateTime(timestamp);
            memberPoint.setUpdateTime(timestamp);
            memberPoint.setDeleteTime(0L);
            laMemberPointMapper.insert(memberPoint);
        }
        
        // 如果选择了福利类型，则创建会员福利领取记录
        if (registerValidate.getBenefitType() != null && !registerValidate.getBenefitType().isEmpty()) {
            MemberBenefitRecord record = new MemberBenefitRecord();
            record.setUserId(userId);
            
            // 根据选择的福利类型设置福利名称和图标
            String benefitType = registerValidate.getBenefitType();
            BenefitTypeEnum benefitTypeEnum = BenefitTypeEnum.getByCode(benefitType);
            
            record.setBenefitName(benefitTypeEnum.getName());
            record.setBenefitIcon(benefitTypeEnum.getIcon());
            record.setRegisterTime(timestamp);
            record.setIsDistributed(0); // 默认未发放
            record.setDistributeTime(0L);
            record.setDistributor("");
            record.setRemark("用户注册会员时选择的权益");
            record.setIsDelete(0);
            record.setCreateTime(timestamp);
            record.setUpdateTime(timestamp);
            record.setDeleteTime(0L);
            
            memberBenefitRecordMapper.insert(record);
        }
    }
    
    /**
     * 获取会员信息
     *
     * @param userId 用户ID
     * @return Map<String, Object> 会员信息
     */
    @Override
    public Map<String, Object> getMemberInfo(Integer userId) {
        if (userId == null || userId <= 0) {
            return null;
        }
        // 查询会员卡信息
        QueryWrapper<MemberCard> cardQueryWrapper = new QueryWrapper<>();
        cardQueryWrapper.eq("user_id", userId)
                .eq("is_active", 1)
                .eq("is_delete", 0);
        MemberCard memberCard = laMemberCardMapper.selectOne(cardQueryWrapper);
        // 如果用户不是会员，返回null
        if (memberCard == null) {
            return null;
        }
        // 检查会员是否过期（如果expireTime > 0）
        if (memberCard.getExpireTime() > 0 && memberCard.getExpireTime() < TimeUtils.timestamp()) {
            return null; // 会员已过期
        }
        // 查询会员等级信息
        QueryWrapper<MemberLevel> levelQueryWrapper = new QueryWrapper<>();
        levelQueryWrapper.eq("level", memberCard.getLevel())
                .eq("status", 1)
                .eq("is_delete", 0);
        MemberLevel memberLevel = laMemberLevelMapper.selectOne(levelQueryWrapper);

        if (memberLevel == null) {
            return null; // 会员等级不存在
        }

        // 构建并返回会员信息
        Map<String, Object> memberInfo = new HashMap<>();
        memberInfo.put("id", memberCard.getId());
        memberInfo.put("user_id", memberCard.getUserId());
        memberInfo.put("card_no", memberCard.getCardNo());
        memberInfo.put("level", memberCard.getLevel());
        memberInfo.put("level_name", memberLevel.getName());
        memberInfo.put("level_icon", memberLevel.getIcon());
        memberInfo.put("discount", memberLevel.getDiscount());
        memberInfo.put("total_recharge", memberCard.getTotalRecharge());
        
        // 格式化时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String createTimeStr = sdf.format(new Date(memberCard.getCreateTime() * 1000));
        memberInfo.put("create_time", createTimeStr);
        
        if (memberCard.getExpireTime() > 0) {
            String expireTimeStr = sdf.format(new Date(memberCard.getExpireTime() * 1000));
            memberInfo.put("expire_time", expireTimeStr);
        } else {
            memberInfo.put("expire_time", "永久有效");
        }

        // 获取会员积分信息并添加到会员信息中
        QueryWrapper<MemberPoint> pointQueryWrapper = new QueryWrapper<>();
        pointQueryWrapper.eq("user_id", userId)
                .eq("is_delete", 0);
        MemberPoint memberPoint = laMemberPointMapper.selectOne(pointQueryWrapper);
        
        Map<String, Object> pointsInfo = new HashMap<>();
        if (memberPoint == null) {
            pointsInfo.put("total_point", 0);
            pointsInfo.put("available_point", 0);
            pointsInfo.put("used_point", 0);
            pointsInfo.put("expired_point", 0);
        } else {
            pointsInfo.put("available_point", memberPoint.getAvailablePoint());
            pointsInfo.put("used_point", memberPoint.getUsedPoint());
            pointsInfo.put("expired_point", memberPoint.getExpiredPoint());
        }
        
        memberInfo.put("points", pointsInfo);

        return memberInfo;
    }

    /**
     * 获取会员权益
     *
     * @param userId 用户ID
     * @return Map<String, Object> 会员权益信息
     */
    @Override
    public Map<String, Object> getMemberBenefits(Integer userId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取会员信息，确认用户是会员
        Map<String, Object> memberInfo = this.getMemberInfo(userId);
        if (memberInfo == null) {
            result.put("is_member", false);
            result.put("benefits", new ArrayList<>());
            return result;
        }
        
        Integer level = Integer.parseInt(memberInfo.get("level").toString());
        
        // 查询该会员等级的所有权益
        QueryWrapper<MemberBenefit> benefitQueryWrapper = new QueryWrapper<>();
        benefitQueryWrapper.eq("level", level)
                .eq("status", 1)
                .eq("is_delete", 0)
                .orderByAsc("sort");
        List<MemberBenefit> benefitList = laMemberBenefitMapper.selectList(benefitQueryWrapper);
        
        // 构建权益数据
        List<Map<String, Object>> benefitMapList = new ArrayList<>();
        for (MemberBenefit benefit : benefitList) {
            Map<String, Object> benefitMap = new HashMap<>();
            benefitMap.put("id", benefit.getId());
            benefitMap.put("name", benefit.getName());
            // 使用UrlUtils.toAbsoluteUrl()处理福利图标的URL，将相对路径转换为绝对路径
            // 增加对空值的处理，避免传入null值导致异常
            String imageUrl = benefit.getImage();
            benefitMap.put("icon", imageUrl != null && !imageUrl.isEmpty() ? UrlUtils.toAbsoluteUrl(imageUrl) : "");
            benefitMap.put("type", benefit.getType());
            benefitMap.put("description", benefit.getDescription());
            benefitMapList.add(benefitMap);
        }
        
        result.put("is_member", true);
        result.put("benefits", benefitMapList);
        return result;
    }

    /**
     * 获取会员积分信息
     *
     * @param userId 用户ID
     * @return Map<String, Object> 会员积分信息
     */
    @Override
    public Map<String, Object> getMemberPoints(Integer userId) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查用户ID是否有效
        if (userId == null || userId <= 0) {
            return null;
        }
        
        // 查询该用户的会员积分信息
        QueryWrapper<MemberPoint> pointQueryWrapper = new QueryWrapper<>();
        pointQueryWrapper.eq("user_id", userId)
                .eq("is_delete", 0);
        MemberPoint memberPoint = laMemberPointMapper.selectOne(pointQueryWrapper);
        
        // 如果没有积分记录，创建一个空记录
        if (memberPoint == null) {
            result.put("total_point", 0);
            result.put("available_point", 0);
            result.put("used_point", 0);
            result.put("expired_point", 0);
        } else {
            result.put("available_point", memberPoint.getAvailablePoint());
            result.put("used_point", memberPoint.getUsedPoint());
            result.put("expired_point", memberPoint.getExpiredPoint());
        }
        
        return result;
    }
    
    /**
     * 获取会员积分记录
     *
     * @param userId 用户ID
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return Map<String, Object> 积分记录及分页信息
     */
    @Override
    public Map<String, Object> getPointLogs(Integer userId, Integer pageNo, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查用户ID是否有效
        if (userId == null || userId <= 0) {
            return null;
        }
        
        // 分页查询用户的积分记录
        IPage<MemberPointLog> iPage = new Page<>(pageNo, pageSize);
        QueryWrapper<MemberPointLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("is_delete", 0)
                .orderByDesc("create_time");
        IPage<MemberPointLog> pageResult = laMemberPointLogMapper.selectPage(iPage, queryWrapper);
        
        // 构建结果数据
        List<Map<String, Object>> records = new ArrayList<>();
        for (MemberPointLog log : pageResult.getRecords()) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", log.getId());
            item.put("type", log.getType());
            item.put("type_name", getTypeName(log.getType()));
            item.put("point", log.getPoint());
            item.put("description", log.getDescription());
            item.put("remark", log.getRemark());
            item.put("relation_id", log.getRelationId());
            
            // 格式化时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String createTimeStr = sdf.format(new Date(log.getCreateTime() * 1000));
            item.put("create_time", createTimeStr);
            
            records.add(item);
        }
        
        // 构建分页信息
        Map<String, Object> pageInfo = new HashMap<>();
        pageInfo.put("page_no", pageNo);
        pageInfo.put("page_size", pageSize);
        pageInfo.put("total_page", pageResult.getPages());
        pageInfo.put("total", pageResult.getTotal());
        
        result.put("list", records);
        result.put("page_info", pageInfo);
        
        return result;
    }
    
    /**
     * 获取积分类型名称
     *
     * @param type 类型ID
     * @return String
     */
    private String getTypeName(Integer type) {
        Map<Integer, String> typeMap = new LinkedHashMap<>();
        typeMap.put(1, "社区点赞");
        typeMap.put(2, "社区评论");
        typeMap.put(3, "社区转发");
        typeMap.put(4, "社区发帖");
        typeMap.put(5, "扫码得积分");
        typeMap.put(6, "消费得积分");
        typeMap.put(7, "充值得积分");
        typeMap.put(8, "积分兑换");
        typeMap.put(9, "积分过期");
        typeMap.put(10, "管理员操作");
        typeMap.put(11, "其他");
        
        return typeMap.getOrDefault(type, "未知类型");
    }

    /**
     * 获取会员福利领取记录
     *
     * @param userId 用户ID
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return Map<String, Object> 会员福利领取记录
     */
    @Override
    public Map<String, Object> getBenefitRecords(Integer userId, Integer pageNo, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        
        // 查询会员福利领取记录
        QueryWrapper<MemberBenefitRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("is_delete", 0)
                .orderByDesc("register_time");
        
        IPage<MemberBenefitRecord> iPage = memberBenefitRecordMapper.selectPage(
                new Page<>(pageNo, pageSize), queryWrapper);
        
        // 处理记录
        List<Map<String, Object>> records = new ArrayList<>();
        for (MemberBenefitRecord record : iPage.getRecords()) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", record.getId());
            map.put("benefit_name", record.getBenefitName());
            map.put("benefit_icon", UrlUtils.toAbsoluteUrl(record.getBenefitIcon()));
            map.put("register_time", TimeUtils.timestampToDate(record.getRegisterTime()));
            map.put("is_distributed", record.getIsDistributed());
            
            if (record.getDistributeTime() > 0) {
                map.put("distribute_time", TimeUtils.timestampToDate(record.getDistributeTime()));
            } else {
                map.put("distribute_time", "");
            }
            
            records.add(map);
        }
        
        result.put("list", records);
        result.put("count", iPage.getTotal());
        result.put("pageSize", iPage.getSize());
        result.put("pageNo", iPage.getCurrent());
        
        return result;
    }

    /**
     * 领取会员福利
     *
     * @param userId 用户ID
     * @param benefitName 福利名称
     * @param benefitIcon 福利图标
     * @return Map<String, Object> 领取结果
     */
    @Override
    public Map<String, Object> receiveBenefit(Integer userId, String benefitName, String benefitIcon) {
        Map<String, Object> result = new HashMap<>();
        
        if (userId == null || userId <= 0) {
            result.put("success", false);
            result.put("message", "用户ID无效");
            return result;
        }
        
        if (benefitName == null || benefitName.isEmpty()) {
            result.put("success", false);
            result.put("message", "福利名称不能为空");
            return result;
        }
        
        if (benefitIcon == null || benefitIcon.isEmpty()) {
            result.put("success", false);
            result.put("message", "福利图标不能为空");
            return result;
        }
        
        // 验证用户是否为会员
        QueryWrapper<MemberCard> cardQueryWrapper = new QueryWrapper<>();
        cardQueryWrapper.eq("user_id", userId)
                .eq("is_active", 1)
                .eq("is_delete", 0);
        MemberCard memberCard = laMemberCardMapper.selectOne(cardQueryWrapper);
        
        if (memberCard == null) {
            result.put("success", false);
            result.put("message", "您还不是会员，无法领取会员福利");
            return result;
        }
        
        // 检查会员是否过期（如果expireTime > 0）
        if (memberCard.getExpireTime() > 0 && memberCard.getExpireTime() < TimeUtils.timestamp()) {
            result.put("success", false);
            result.put("message", "您的会员已过期，无法领取会员福利");
            return result;
        }
        
        // 检查会员等级是否为黄金会员(level=2)
        if (memberCard.getLevel() != 2) {
            result.put("success", false);
            result.put("message", "只有黄金会员才能领取会员福利");
            return result;
        }
        
        // 查询会员等级信息
        QueryWrapper<MemberLevel> levelQueryWrapper = new QueryWrapper<>();
        levelQueryWrapper.eq("level", memberCard.getLevel())
                .eq("status", 1)
                .eq("is_delete", 0);
        MemberLevel memberLevel = laMemberLevelMapper.selectOne(levelQueryWrapper);
        
        if (memberLevel == null) {
            result.put("success", false);
            result.put("message", "会员等级信息不存在");
            return result;
        }
        
        // 检查是否已经领取过该名称的福利
        QueryWrapper<MemberBenefitRecord> recordQueryWrapper = new QueryWrapper<>();
        recordQueryWrapper.eq("user_id", userId)
                .eq("benefit_name", benefitName)
                .eq("is_delete", 0);
        MemberBenefitRecord existRecord = memberBenefitRecordMapper.selectOne(recordQueryWrapper);
        
        if (existRecord != null) {
            result.put("success", false);
            result.put("message", "您已经领取过该福利，不能重复领取");
            return result;
        }
        
        // 创建福利领取记录
        long timestamp = TimeUtils.timestamp();
        MemberBenefitRecord record = new MemberBenefitRecord();
        record.setUserId(userId);
        record.setBenefitName(benefitName);
        record.setBenefitIcon(benefitIcon);
        record.setRegisterTime(timestamp);
        record.setIsDistributed(0); // 默认未发放
        record.setDistributeTime(0L);
        record.setDistributor("");
        record.setRemark("小程序领取的会员福利");
        record.setIsDelete(0);
        record.setCreateTime(timestamp);
        record.setUpdateTime(timestamp);
        record.setDeleteTime(0L);
        
        memberBenefitRecordMapper.insert(record);
        
        // 查询用户信息，获取用户昵称
        User user = userMapper.selectById(userId);
        String nickName = user != null ? user.getNickname() : "";
        
        result.put("success", true);
        result.put("message", "福利领取成功，我们会尽快为您发放");
        result.put("data", new HashMap<String, Object>() {{
            put("id", record.getId());
            put("userId", userId);
            put("nickName", nickName);
            put("benefitName", benefitName);
            put("benefitIcon", benefitIcon);
            put("registerTime", timestamp);
            put("status", "待发放");
        }});
        
        return result;
    }
} 