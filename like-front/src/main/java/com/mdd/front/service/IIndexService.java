package com.mdd.front.service;

import java.util.List;
import java.util.Map;

/**
 * 首页服务接口类
 */
public interface IIndexService {

    /**
     * 首页
     *
     * <AUTHOR>
     * @return Map<String, Object>
     */
    Map<String, Object> index(String brand);

    /**
     * 装修
     *
     * <AUTHOR>
     * @param id 装修ID
     * @return Map<String, Object>
     */
    Map<String, Object> decorate(Integer id);

    /**
     * 配置
     *
     * <AUTHOR>
     * @return Map<String, Object>
     */
    Map<String, Object> config();

    /**
     * 政策
     *
     * <AUTHOR>
     * @param type 类型 service=服务协议,privacy=隐私协议
     * @return Map<String, Object>
     */
    Map<String, String> policy(String type);

    /**
     * 热搜
     *
     * <AUTHOR>
     * @return List<String>
     */
    List<String> hotSearch();

    /**
     * 用户访问量统计
     *
     * <AUTHOR>
     * @param terminal Integer
     */
    void visit(Integer terminal);

}
