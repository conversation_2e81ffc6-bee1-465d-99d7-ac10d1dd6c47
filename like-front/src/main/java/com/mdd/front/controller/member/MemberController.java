package com.mdd.front.controller.member;

import com.mdd.common.core.AjaxResult;
import com.mdd.front.LikeFrontThreadLocal;
import com.mdd.front.service.member.IMemberService;
import com.mdd.front.validate.member.MemberBenefitReceiveValidate;
import com.mdd.front.validate.member.MemberRegisterValidate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 会员控制器
 */
@RestController
@RequestMapping("api/member")
@Api(tags = "会员接口")
public class MemberController {

    @Resource
    IMemberService iMemberService;

    /**
     * 会员注册
     *
     * <AUTHOR> Name
     * @param registerValidate 注册参数
     * @return AjaxResult
     */
    @PostMapping("/register")
    @ApiOperation(value="会员注册")
    public AjaxResult register(@Validated @RequestBody MemberRegisterValidate registerValidate) {
        // 获取当前登录用户ID
        Integer userId = LikeFrontThreadLocal.getUserId();
        registerValidate.setUserId(userId);
        iMemberService.register(registerValidate);
        return AjaxResult.success();
    }
    
    /**
     * 获取会员信息
     *
     * @return AjaxResult
     */
    @GetMapping("/info")
    @ApiOperation(value="获取会员信息")
    public AjaxResult info() {
        // 获取当前登录用户ID
        Integer userId = LikeFrontThreadLocal.getUserId();
        // 获取会员信息
        Map<String, Object> memberInfo = iMemberService.getMemberInfo(userId);
        // 如果不是会员，返回null
        if (memberInfo == null) {
            return AjaxResult.success((Object) null);
        }
        return AjaxResult.success(memberInfo);
    }
    
    /**
     * 获取会员权益
     *
     * @return AjaxResult
     */
    @GetMapping("/benefits")
    @ApiOperation(value="获取会员权益")
    public AjaxResult benefits() {
        // 获取当前登录用户ID
        Integer userId = LikeFrontThreadLocal.getUserId();
        
        // 获取会员权益
        Map<String, Object> benefits = iMemberService.getMemberBenefits(userId);
        
        return AjaxResult.success(benefits);
    }
    
    /**
     * 获取会员积分信息
     *
     * @return AjaxResult
     */
    @GetMapping("/points")
    @ApiOperation(value="获取会员积分信息")
    public AjaxResult points() {
        // 获取当前登录用户ID
        Integer userId = LikeFrontThreadLocal.getUserId();
        
        // 获取会员积分信息
        Map<String, Object> pointsInfo = iMemberService.getMemberPoints(userId);
        
        return AjaxResult.success(pointsInfo);
    }
    
    /**
     * 获取积分记录
     *
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return AjaxResult
     */
    @GetMapping("/point-logs")
    @ApiOperation(value="获取积分记录")
    public AjaxResult pointLogs(
            @ApiParam(value = "页码", required = false, defaultValue = "1") 
            @RequestParam(value = "page_no", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数", required = false, defaultValue = "10") 
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize) {
        // 获取当前登录用户ID
        Integer userId = LikeFrontThreadLocal.getUserId();
        
        // 获取积分记录
        Map<String, Object> logs = iMemberService.getPointLogs(userId, pageNo, pageSize);
        
        return AjaxResult.success(logs);
    }

    /**
     * 获取会员福利领取记录
     *
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return AjaxResult
     */
    @GetMapping("/benefit-records")
    @ApiOperation(value="获取会员福利领取记录")
    public AjaxResult benefitRecords(
            @ApiParam(value = "页码", required = false, defaultValue = "1") 
            @RequestParam(value = "page_no", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数", required = false, defaultValue = "10") 
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize) {
        // 获取当前登录用户ID
        Integer userId = LikeFrontThreadLocal.getUserId();
        
        // 获取会员福利领取记录
        Map<String, Object> records = iMemberService.getBenefitRecords(userId, pageNo, pageSize);
        
        return AjaxResult.success(records);
    }
    
    /**
     * 小程序领取会员福利
     *
     * @param benefitReceiveValidate 福利参数
     * @return AjaxResult
     */
    @PostMapping("/receive-benefit")
    @ApiOperation(value="小程序领取会员福利")
    public AjaxResult receiveBenefit(@Validated @RequestBody MemberBenefitReceiveValidate benefitReceiveValidate) {
        // 获取当前登录用户ID
        Integer userId = LikeFrontThreadLocal.getUserId();
        
        // 调用会员服务领取福利
        Map<String, Object> result = iMemberService.receiveBenefit(
            userId, 
            benefitReceiveValidate.getBenefitName(), 
            benefitReceiveValidate.getBenefitIcon()
        );
        
        if ((Boolean) result.get("success")) {
            return AjaxResult.success(result.get("message").toString(), result.get("data"));
        } else {
            return AjaxResult.failed(result.get("message").toString());
        }
    }
} 