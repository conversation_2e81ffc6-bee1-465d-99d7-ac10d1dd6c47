package com.mdd.front.controller.three;

import com.mdd.common.core.AjaxResult;
import com.mdd.front.service.three.ILaThreeWelfareOfficerService;
import com.mdd.front.vo.ThreeWelfareOfficerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 小程序福利官薅羊毛接口
 */
@RestController
@RequestMapping("api/three/welfare")
@Api(tags = "小程序福利官薅羊毛")
public class LaThreeWelfareOfficerController {

    @Resource
    ILaThreeWelfareOfficerService iMnpThreeWelfareOfficerService;

    /**
     * 获取福利官薅羊毛信息
     */
    @GetMapping("/info")
    @ApiOperation(value="获取福利官薅羊毛信息")
    public AjaxResult<Object> info() {
        ThreeWelfareOfficerVo vo = iMnpThreeWelfareOfficerService.info();
        return AjaxResult.success(vo);
    }

} 