package com.mdd.front.controller;

import com.mdd.common.aop.NotLogin;
import com.mdd.common.core.AjaxResult;
import com.mdd.front.service.IIndexBannerService;
import com.mdd.front.vo.banner.IndexBannerListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 轮播图控制器
 */
@RestController
@RequestMapping("api/banner")
@Api(tags = "轮播图管理")
public class BannerController {

    @Resource
    IIndexBannerService iIndexBannerService;

    @NotLogin
    @GetMapping("/index/all")
    @ApiOperation(value="获取首页所有启用的轮播图")
    public AjaxResult<List<IndexBannerListVo>> getAllEnabledBanner() {
        List<IndexBannerListVo> list = iIndexBannerService.getAllEnabledBanner();
        return AjaxResult.success(list);
    }
    
    @NotLogin
    @GetMapping("/index/large")
    @ApiOperation(value="获取首页大图轮播图")
    public AjaxResult<List<IndexBannerListVo>> getLargeBanners() {
        List<IndexBannerListVo> list = iIndexBannerService.getLargeBanners();
        return AjaxResult.success(list);
    }
    
    @NotLogin
    @GetMapping("/index/small")
    @ApiOperation(value="获取首页小图轮播图")
    public AjaxResult<List<IndexBannerListVo>> getSmallBanners() {
        List<IndexBannerListVo> list = iIndexBannerService.getSmallBanners();
        return AjaxResult.success(list);
    }
} 