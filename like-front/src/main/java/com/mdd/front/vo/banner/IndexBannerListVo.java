package com.mdd.front.vo.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("首页轮播图列表Vo")
public class IndexBannerListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "图片地址")
    private String image;

    @ApiModelProperty(value = "类型：1=大图，2=小图")
    private Integer type;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "链接地址")
    private String link;

    @ApiModelProperty(value = "状态：0=禁用，1=启用")
    private Integer status;
} 