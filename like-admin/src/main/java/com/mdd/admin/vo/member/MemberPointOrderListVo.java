package com.mdd.admin.vo.member;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 积分订单列表VO
 */
@Data
public class MemberPointOrderListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;            // 主键ID
    private String orderSn;        // 订单编号
    private Integer userId;        // 用户ID
    private String nickname;       // 用户昵称
    private String avatar;         // 用户头像
    private Integer orderId;       // 关联订单ID
    private String orderOrderSn;   // 关联订单号
    private Integer payPoint;      // 支付积分
    private Integer status;        // 状态: [1=待付款, 2=已取消, 3=已付款, 4=已发货, 5=已完成]
    private String statusName;     // 状态名称
    private String createTime;     // 创建时间
} 