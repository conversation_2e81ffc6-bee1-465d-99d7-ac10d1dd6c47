package com.mdd.admin.validate.member;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 会员卡验证类
 */
@Data
public class MemberCardValidate {

    private Integer id;         // 主键ID
    private Integer userId;     // 用户ID
    private Integer level;      // 会员等级: [1=白银会员, 2=黄金会员, 3=钻石会员, 4=黑卡会员]
    private Integer isActive;   // 是否激活: [0=否, 1=是]
    private Long expireTime;    // 过期时间（为0表示永久有效）

} 