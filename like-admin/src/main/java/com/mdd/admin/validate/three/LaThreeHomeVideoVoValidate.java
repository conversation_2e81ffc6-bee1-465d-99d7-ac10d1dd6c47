package com.mdd.admin.validate.three;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class LaThreeHomeVideoVoValidate {

    private Integer id;

    @NotEmpty(message = "视频缩略图地址不能为空")
    private String videoThumbnailAddress;

    @NotEmpty(message = "视频地址不能为空")
    private String videoAddress;

    @NotEmpty(message = "视频后缀不能为空")
    private String videoSuffix;

    @NotEmpty(message = "所属品牌不能为空")
    private String belongingBrand;

    private Integer isStop;
} 