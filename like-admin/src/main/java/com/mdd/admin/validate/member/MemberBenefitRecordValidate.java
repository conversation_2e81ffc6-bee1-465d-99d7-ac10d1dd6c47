package com.mdd.admin.validate.member;

import lombok.Data;

/**
 * 会员福利领取记录验证类
 */
@Data
public class MemberBenefitRecordValidate {

    private Integer id;              // 主键ID
    private Integer userId;          // 会员ID
    private String benefitName;      // 福利名称
    private String benefitIcon;      // 福利图标
    private Integer isDistributed;   // 是否发放: [0=否, 1=是]
    private String distributor;      // 发放人
    private String remark;           // 备注
    private String benefitType;      // 福利类型代码
} 