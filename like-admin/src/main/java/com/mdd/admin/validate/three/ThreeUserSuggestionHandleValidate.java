package com.mdd.admin.validate.three;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户建议处理参数校验
 */
@Data
@ApiModel("用户建议处理参数")
public class ThreeUserSuggestionHandleValidate implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "处理状态不能为空")
    @ApiModelProperty(value = "处理状态：0=未处理，1=已处理", required = true)
    private Integer status;

    @ApiModelProperty(value = "处理备注")
    private String remark;
} 