package com.mdd.admin.service;

import com.alibaba.fastjson2.JSONArray;
import com.mdd.admin.validate.system.SystemDeptCreateValidate;
import com.mdd.admin.validate.system.SystemDeptSearchValidate;
import com.mdd.admin.validate.system.SystemDeptUpdateValidate;
import com.mdd.admin.vo.system.SystemAuthDeptVo;

import java.util.List;

/**
 * 系统部门服务接口类
 */
public interface ISystemAuthDeptService {

    /**
     * 部门所有
     *
     * <AUTHOR>
     * @return List<SystemDeptVo>
     */
    List<SystemAuthDeptVo> all();

    /**
     * 部门列表
     *
     * <AUTHOR>
     * @param searchValidate 搜索参数
     * @return JSONArray
     */
    JSONArray list(SystemDeptSearchValidate searchValidate);

    /**
     * 部门详情
     *
     * <AUTHOR>
     * @param id 主键
     * @return SysMenu
     */
    SystemAuthDeptVo detail(Integer id);

    /**
     * 部门新增
     *
     * <AUTHOR>
     * @param createValidate 参数
     */
    void add(SystemDeptCreateValidate createValidate);

    /**
     * 部门编辑
     *
     * <AUTHOR>
     * @param updateValidate 参数
     */
    void edit(SystemDeptUpdateValidate updateValidate);

    /**
     * 部门删除
     *
     * <AUTHOR>
     * @param id 主键
     */
    void del(Integer id);

}
