package com.mdd.admin.service.beauty;

import com.mdd.admin.validate.beauty.BeautyFeedBindTopicValidate;
import com.mdd.admin.vo.beauty.BeautyFeedListVo;
import com.mdd.common.core.PageResult;

/**
 * 美妆社区帖子管理服务接口
 */
public interface IBeautyFeedService {

    /**
     * 获取帖子列表（分页）
     *
     * @param pageNo 当前页码
     * @param pageSize 每页数量
     * @param title 帖子标题（可选）
     * @param userId 用户ID（可选）
     * @return PageResult<BeautyFeedListVo>
     */
    PageResult<BeautyFeedListVo> list(Integer pageNo, Integer pageSize, String title, Integer userId);

    /**
     * 获取帖子详情
     *
     * @param id 帖子ID
     * @return BeautyFeedListVo
     */
    BeautyFeedListVo detail(Integer id);

    /**
     * 删除帖子
     *
     * @param id 帖子ID
     */
    void delete(Integer id);
    
    /**
     * 绑定话题到帖子
     *
     * @param validate 绑定参数
     */
    void bindTopic(BeautyFeedBindTopicValidate validate);
} 