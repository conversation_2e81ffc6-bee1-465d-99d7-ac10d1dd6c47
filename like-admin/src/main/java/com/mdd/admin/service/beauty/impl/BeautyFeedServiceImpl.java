package com.mdd.admin.service.beauty.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mdd.admin.service.beauty.IBeautyFeedService;
import com.mdd.admin.validate.beauty.BeautyFeedBindTopicValidate;
import com.mdd.admin.vo.beauty.BeautyFeedListVo;
import com.mdd.common.core.PageResult;
import com.mdd.common.entity.beauty.BeautyFeed;
import com.mdd.common.entity.beauty.BeautyFeedGoods;
import com.mdd.common.entity.beauty.BeautyFeedLike;
import com.mdd.common.entity.beauty.BeautyFeedTopic;
import com.mdd.common.entity.beauty.BeautyTopic;
import com.mdd.common.entity.goods.Goods;
import com.mdd.common.entity.user.User;
import com.mdd.common.exception.OperateException;
import com.mdd.common.mapper.beauty.BeautyFeedGoodsMapper;
import com.mdd.common.mapper.beauty.BeautyFeedLikeMapper;
import com.mdd.common.mapper.beauty.BeautyFeedMapper;
import com.mdd.common.mapper.beauty.BeautyFeedTopicMapper;
import com.mdd.common.mapper.beauty.BeautyTopicMapper;
import com.mdd.common.mapper.goods.GoodsMapper;
import com.mdd.common.mapper.user.UserMapper;
import com.mdd.common.util.StringUtils;
import com.mdd.common.util.TimeUtils;
import com.mdd.common.util.UrlUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 美妆社区帖子服务实现类
 */
@Service
public class BeautyFeedServiceImpl implements IBeautyFeedService {

    @Resource
    private BeautyFeedMapper beautyFeedMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private BeautyFeedTopicMapper beautyFeedTopicMapper;

    @Resource
    private BeautyTopicMapper beautyTopicMapper;

    @Resource
    private BeautyFeedLikeMapper beautyFeedLikeMapper;
    
    @Resource
    private BeautyFeedGoodsMapper beautyFeedGoodsMapper;
    
    @Resource
    private GoodsMapper goodsMapper;

    /**
     * 获取帖子列表
     *
     * @param pageNo 当前页码
     * @param pageSize 每页数量
     * @param title 帖子标题
     * @param userId 用户ID
     * @return PageResult<BeautyFeedListVo>
     */
    @Override
    public PageResult<BeautyFeedListVo> list(Integer pageNo, Integer pageSize, String title, Integer userId) {
        // 构建查询条件
        QueryWrapper<BeautyFeed> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del", 0);
        
        // 标题查询
        if (StringUtils.isNotEmpty(title)) {
            queryWrapper.like("title", title);
        }
        
        // 用户ID查询
        if (userId != null && userId > 0) {
            queryWrapper.eq("user_id", userId);
        }
        
        // 排序
        queryWrapper.orderByDesc("create_time");

        // 查询数据
        IPage<BeautyFeed> iPage = beautyFeedMapper.selectPage(new Page<>(pageNo, pageSize), queryWrapper);
        List<BeautyFeed> feeds = iPage.getRecords();
        
        if (feeds.isEmpty()) {
            return PageResult.iPageHandle(0L, pageNo.longValue(), pageSize.longValue(), new LinkedList<>());
        }
        
        // 1. 收集所有用户ID并批量查询用户信息
        Set<Integer> userIds = feeds.stream()
                .map(BeautyFeed::getUserId)
                .filter(id -> id != null && id > 0)
                .collect(Collectors.toSet());
        
        Map<Integer, User> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<User> users = userMapper.selectBatchIds(userIds);
            userMap = users.stream().collect(Collectors.toMap(User::getId, user -> user, (k1, k2) -> k1));
        }
        
        // 2. 收集所有帖子ID
        List<Integer> feedIds = feeds.stream()
                .map(BeautyFeed::getId)
                .collect(Collectors.toList());
        
        // 3. 批量查询帖子关联话题
        Map<Integer, List<BeautyFeedTopic>> feedTopicMap = new HashMap<>();
        if (!feedIds.isEmpty()) {
            QueryWrapper<BeautyFeedTopic> topicQueryWrapper = new QueryWrapper<>();
            topicQueryWrapper.in("feed_id", feedIds);
            List<BeautyFeedTopic> allFeedTopics = beautyFeedTopicMapper.selectList(topicQueryWrapper);
            
            // 按帖子ID分组
            for (BeautyFeedTopic feedTopic : allFeedTopics) {
                feedTopicMap.computeIfAbsent(feedTopic.getFeedId(), k -> new ArrayList<>()).add(feedTopic);
            }
        }
        
        // 4. 批量查询话题信息
        Set<Integer> topicIds = new HashSet<>();
        for (List<BeautyFeedTopic> topicList : feedTopicMap.values()) {
            for (BeautyFeedTopic feedTopic : topicList) {
                if (feedTopic.getTopicId() != null && feedTopic.getTopicId() > 0) {
                    topicIds.add(feedTopic.getTopicId());
                }
            }
        }
        
        Map<Integer, BeautyTopic> topicMap = new HashMap<>();
        if (!topicIds.isEmpty()) {
            List<BeautyTopic> topics = beautyTopicMapper.selectBatchIds(topicIds);
            topicMap = topics.stream().collect(Collectors.toMap(BeautyTopic::getId, topic -> topic, (k1, k2) -> k1));
        }
        
        // 5. 批量查询帖子关联商品
        Map<Integer, BeautyFeedGoods> feedGoodsMap = new HashMap<>();
        if (!feedIds.isEmpty()) {
            QueryWrapper<BeautyFeedGoods> goodsQueryWrapper = new QueryWrapper<>();
            goodsQueryWrapper.in("feed_id", feedIds);
            List<BeautyFeedGoods> feedGoodsList = beautyFeedGoodsMapper.selectList(goodsQueryWrapper);
            
            // 转为Map方便查找
            feedGoodsMap = feedGoodsList.stream()
                    .collect(Collectors.toMap(BeautyFeedGoods::getFeedId, goods -> goods, (k1, k2) -> k1));
        }
        
        // 6. 批量查询商品信息
        Set<Integer> goodsIds = feedGoodsMap.values().stream()
                .map(BeautyFeedGoods::getGoodsId)
                .filter(id -> id != null && id > 0)
                .collect(Collectors.toSet());
        
        Map<Integer, Goods> goodsMap = new HashMap<>();
        if (!goodsIds.isEmpty()) {
            List<Goods> goodsList = goodsMapper.selectBatchIds(goodsIds);
            goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, goods -> goods, (k1, k2) -> k1));
        }
        
        // 7. 组装数据
        List<BeautyFeedListVo> resultList = new LinkedList<>();
        for (BeautyFeed feed : feeds) {
            BeautyFeedListVo vo = new BeautyFeedListVo();
            BeanUtils.copyProperties(feed, vo);
            
            // 转换图片列表
            if (StringUtils.isNotEmpty(feed.getImages())) {
                vo.setImageList(Arrays.asList(feed.getImages().split(",")));
            } else {
                vo.setImageList(new ArrayList<>());
            }
            
            // 获取用户信息
            User user = userMap.get(feed.getUserId());
            if (user != null) {
                vo.setNickname(user.getNickname());
                // 转换头像为绝对路径
                vo.setAvatar(UrlUtils.toAbsoluteUrl(user.getAvatar()));
            }
            
            // 获取关联话题
            List<BeautyFeedTopic> feedTopics = feedTopicMap.getOrDefault(feed.getId(), Collections.emptyList());
            if (!feedTopics.isEmpty()) {
                List<String> topicNames = new ArrayList<>();
                for (BeautyFeedTopic feedTopic : feedTopics) {
                    BeautyTopic topic = topicMap.get(feedTopic.getTopicId());
                    if (topic != null) {
                        topicNames.add(topic.getName());
                    }
                }
                vo.setTopics(String.join(",", topicNames));
            }
            
            // 获取关联商品信息
            BeautyFeedGoods feedGoods = feedGoodsMap.get(feed.getId());
            if (feedGoods != null) {
                Goods goods = goodsMap.get(feedGoods.getGoodsId());
                if (goods != null) {
                    vo.setGoodsId(goods.getId());
                    vo.setGoodsName(goods.getName());
                    vo.setGoodsSales(goods.getSalesNum());
                    vo.setGoodsPrice(goods.getMinPrice());
                    vo.setGoodsLineationPrice(goods.getMinLineationPrice());
                    // 转换商品图片为绝对路径
                    vo.setGoodsImage(UrlUtils.toAbsoluteUrl(goods.getImage()));
                }
            }
            
            resultList.add(vo);
        }

        // 返回数据
        return PageResult.iPageHandle(iPage.getTotal(), iPage.getCurrent(), iPage.getSize(), resultList);
    }

    /**
     * 获取帖子详情
     *
     * @param id 帖子ID
     * @return BeautyFeedListVo
     */
    @Override
    public BeautyFeedListVo detail(Integer id) {
        BeautyFeed feed = beautyFeedMapper.selectById(id);
        if (feed == null || feed.getDel().equals(1)) {
            throw new OperateException("帖子不存在");
        }
        
        BeautyFeedListVo vo = new BeautyFeedListVo();
        BeanUtils.copyProperties(feed, vo);
        
        // 转换图片列表
        if (StringUtils.isNotEmpty(feed.getImages())) {
            vo.setImageList(Arrays.asList(feed.getImages().split(",")));
        } else {
            vo.setImageList(new ArrayList<>());
        }
        
        // 获取用户信息
        User user = userMapper.selectById(feed.getUserId());
        if (user != null) {
            vo.setNickname(user.getNickname());
            // 转换头像为绝对路径
            vo.setAvatar(UrlUtils.toAbsoluteUrl(user.getAvatar()));
        }
        
        // 获取关联话题
        QueryWrapper<BeautyFeedTopic> topicQueryWrapper = new QueryWrapper<>();
        topicQueryWrapper.eq("feed_id", feed.getId());
        List<BeautyFeedTopic> feedTopics = beautyFeedTopicMapper.selectList(topicQueryWrapper);
        if (!feedTopics.isEmpty()) {
            List<Integer> topicIds = feedTopics.stream().map(BeautyFeedTopic::getTopicId).collect(Collectors.toList());
            
            // 查询话题名称并拼接
            List<String> topicNames = new ArrayList<>();
            for (Integer topicId : topicIds) {
                String topicName = beautyTopicMapper.selectById(topicId).getName();
                topicNames.add(topicName);
            }
            vo.setTopics(String.join(",", topicNames));
        }
        
        // 获取关联商品信息
        QueryWrapper<BeautyFeedGoods> goodsQueryWrapper = new QueryWrapper<>();
        goodsQueryWrapper.eq("feed_id", feed.getId());
        BeautyFeedGoods feedGoods = beautyFeedGoodsMapper.selectOne(goodsQueryWrapper);
        if (feedGoods != null) {
            Goods goods = goodsMapper.selectById(feedGoods.getGoodsId());
            if (goods != null) {
                vo.setGoodsId(goods.getId());
                vo.setGoodsName(goods.getName());
                vo.setGoodsSales(goods.getSalesNum());
                vo.setGoodsPrice(goods.getMinPrice());
                vo.setGoodsLineationPrice(goods.getMinLineationPrice());
                // 转换商品图片为绝对路径
                vo.setGoodsImage(UrlUtils.toAbsoluteUrl(goods.getImage()));
            }
        }
        
        return vo;
    }

    /**
     * 删除帖子
     *
     * @param id 帖子ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        BeautyFeed feed = beautyFeedMapper.selectById(id);
        if (feed == null || feed.getDel().equals(1)) {
            throw new OperateException("帖子不存在");
        }

        // 删除帖子关联话题
        QueryWrapper<BeautyFeedTopic> topicQueryWrapper = new QueryWrapper<>();
        topicQueryWrapper.eq("feed_id", id);
        beautyFeedTopicMapper.delete(topicQueryWrapper);
        
        // 删除帖子关联点赞
        QueryWrapper<BeautyFeedLike> likeQueryWrapper = new QueryWrapper<>();
        likeQueryWrapper.eq("feed_id", id);
        beautyFeedLikeMapper.delete(likeQueryWrapper);
        
        // 删除帖子关联商品
        QueryWrapper<BeautyFeedGoods> goodsQueryWrapper = new QueryWrapper<>();
        goodsQueryWrapper.eq("feed_id", id);
        beautyFeedGoodsMapper.delete(goodsQueryWrapper);
        
        // 软删除帖子
        beautyFeedMapper.deleteById(id);
    }

    /**
     * 绑定话题到帖子
     *
     * @param validate 绑定参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindTopic(BeautyFeedBindTopicValidate validate) {
        // 验证帖子是否存在
        BeautyFeed feed = beautyFeedMapper.selectById(validate.getFeedId());
        if (feed == null || feed.getDel().equals(1)) {
            throw new OperateException("帖子不存在");
        }
        
        // 验证话题是否存在
        BeautyTopic topic = beautyTopicMapper.selectById(validate.getTopicId());
        if (topic == null || topic.getDel().equals(1)) {
            throw new OperateException("话题不存在");
        }
        
        // 先删除当前帖子的所有话题关联
        QueryWrapper<BeautyFeedTopic> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("feed_id", validate.getFeedId());
        List<BeautyFeedTopic> oldTopics = beautyFeedTopicMapper.selectList(deleteWrapper);
        
        // 如果有旧的话题关联，则减少它们的使用次数
        for (BeautyFeedTopic oldTopic : oldTopics) {
            BeautyTopic oldTopicInfo = beautyTopicMapper.selectById(oldTopic.getTopicId());
            if (oldTopicInfo != null && oldTopicInfo.getUseCount() > 0) {
                oldTopicInfo.setUseCount(oldTopicInfo.getUseCount() - 1);
                beautyTopicMapper.updateById(oldTopicInfo);
            }
        }
        
        // 删除旧的关联
        beautyFeedTopicMapper.delete(deleteWrapper);
        
        // 添加新的话题关联
        Long now = System.currentTimeMillis() / 1000;
        BeautyFeedTopic feedTopic = new BeautyFeedTopic();
        feedTopic.setFeedId(validate.getFeedId());
        feedTopic.setTopicId(validate.getTopicId());
        feedTopic.setCreateTime(now);
        beautyFeedTopicMapper.insert(feedTopic);
        
        // 增加新话题的使用次数
        topic.setUseCount(topic.getUseCount() + 1);
        beautyTopicMapper.updateById(topic);
    }
} 