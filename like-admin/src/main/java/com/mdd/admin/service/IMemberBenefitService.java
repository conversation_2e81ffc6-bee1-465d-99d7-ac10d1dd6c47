package com.mdd.admin.service;

import com.mdd.admin.validate.member.MemberBenefitValidate;
import com.mdd.admin.vo.member.MemberBenefitListVo;
import com.mdd.admin.vo.member.MemberBenefitDetailVo;
import com.mdd.common.core.PageResult;

import java.util.Map;

/**
 * 会员福利服务接口
 */
public interface IMemberBenefitService {

    /**
     * 会员福利列表
     *
     * @param params 查询参数
     * @return PageResult<MemberBenefitListVo>
     */
    PageResult<MemberBenefitListVo> list(Map<String, String> params);

    /**
     * 会员福利详情
     *
     * @param id 主键ID
     * @return MemberBenefitDetailVo
     */
    MemberBenefitDetailVo detail(Integer id);

    /**
     * 会员福利新增
     *
     * @param validate 参数
     */
    void add(MemberBenefitValidate validate);

    /**
     * 会员福利编辑
     *
     * @param validate 参数
     */
    void edit(MemberBenefitValidate validate);

    /**
     * 会员福利删除
     *
     * @param id 主键ID
     */
    void del(Integer id);

    /**
     * 设置会员福利状态
     *
     * @param id 主键ID
     * @param status 状态 [0=禁用, 1=启用]
     */
    void status(Integer id, Integer status);

} 