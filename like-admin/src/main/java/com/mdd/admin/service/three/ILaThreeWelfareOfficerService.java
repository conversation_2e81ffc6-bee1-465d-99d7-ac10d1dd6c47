package com.mdd.admin.service.three;

import com.mdd.admin.validate.commons.PageValidate;
import com.mdd.admin.validate.three.ThreeWelfareOfficerValidate;
import com.mdd.admin.vo.three.ThreeWelfareOfficerListVo;
import com.mdd.admin.vo.three.ThreeWelfareOfficerDetailVo;
import com.mdd.common.core.PageResult;

/**
 * 福利官薅羊毛服务接口类
 */
public interface ILaThreeWelfareOfficerService {

    /**
     * 福利官薅羊毛列表
     *
     * @param pageValidate 分页参数
     * @return PageResult<ThreeWelfareOfficerListVo>
     */
    PageResult<ThreeWelfareOfficerListVo> list(PageValidate pageValidate);

    /**
     * 福利官薅羊毛详情
     *
     * @param id 主键ID
     * @return ThreeWelfareOfficerDetailVo
     */
    ThreeWelfareOfficerDetailVo detail(Integer id);

    /**
     * 福利官薅羊毛新增
     *
     * @param threeWelfareOfficerValidate 参数
     */
    void add(ThreeWelfareOfficerValidate threeWelfareOfficerValidate);

    /**
     * 福利官薅羊毛编辑
     *
     * @param id 主键ID
     * @param threeWelfareOfficerValidate 参数
     */
    void edit(Integer id, ThreeWelfareOfficerValidate threeWelfareOfficerValidate);

    /**
     * 福利官薅羊毛删除
     *
     * @param id 主键ID
     */
    void del(Integer id);

    /**
     * 福利官薅羊毛状态更新
     *
     * @param id 主键ID
     */
    void change(Integer id);

} 