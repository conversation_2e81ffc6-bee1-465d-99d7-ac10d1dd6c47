package com.mdd.admin.service.three.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mdd.admin.service.three.ILaThreeWelfareOfficerService;
import com.mdd.admin.validate.commons.PageValidate;
import com.mdd.admin.validate.three.ThreeWelfareOfficerValidate;
import com.mdd.admin.vo.three.ThreeWelfareOfficerDetailVo;
import com.mdd.admin.vo.three.ThreeWelfareOfficerListVo;
import com.mdd.common.core.PageResult;
import com.mdd.common.entity.three.ThreeWelfareOfficer;
import com.mdd.common.exception.OperateException;
import com.mdd.common.mapper.three.LaThreeWelfareOfficerMapper;
import com.mdd.common.util.TimeUtils;
import com.mdd.common.util.UrlUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 福利官薅羊毛服务实现类
 */
@Service
public class LaThreeWelfareOfficerServiceImpl implements ILaThreeWelfareOfficerService {

    @Resource
    LaThreeWelfareOfficerMapper threeWelfareOfficerMapper;

    /**
     * 福利官薅羊毛列表
     *
     * @param pageValidate 分页参数
     * @return PageResult<ThreeWelfareOfficerListVo>
     */
    @Override
    public PageResult<ThreeWelfareOfficerListVo> list(PageValidate pageValidate) {
        Integer pageNo = pageValidate.getPageNo();
        Integer pageSize = pageValidate.getPageSize();

        QueryWrapper<ThreeWelfareOfficer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        queryWrapper.orderByDesc("id");

        IPage<ThreeWelfareOfficer> iPage = threeWelfareOfficerMapper.selectPage(
                new Page<>(pageNo, pageSize), queryWrapper);

        List<ThreeWelfareOfficerListVo> list = new ArrayList<>();
        for (ThreeWelfareOfficer threeWelfareOfficer : iPage.getRecords()) {
            ThreeWelfareOfficerListVo vo = new ThreeWelfareOfficerListVo();
            BeanUtils.copyProperties(threeWelfareOfficer, vo);
            vo.setBackgroundImage(UrlUtils.toAbsoluteUrl(threeWelfareOfficer.getBackgroundImage()));
            vo.setQrcodeImage(UrlUtils.toAbsoluteUrl(threeWelfareOfficer.getQrcodeImage()));
            vo.setCreateTime(TimeUtils.timestampToDate(threeWelfareOfficer.getCreateTime().longValue()));
            vo.setUpdateTime(TimeUtils.timestampToDate(threeWelfareOfficer.getUpdateTime().longValue()));
            list.add(vo);
        }

        return PageResult.iPageHandle(iPage.getTotal(), iPage.getCurrent(), iPage.getSize(), list);
    }

    /**
     * 福利官薅羊毛详情
     *
     * @param id 主键ID
     * @return ThreeWelfareOfficerDetailVo
     */
    @Override
    public ThreeWelfareOfficerDetailVo detail(Integer id) {
        ThreeWelfareOfficer threeWelfareOfficer = threeWelfareOfficerMapper.selectById(id);
        if (threeWelfareOfficer == null) {
            throw new OperateException("数据不存在!");
        }

        ThreeWelfareOfficerDetailVo vo = new ThreeWelfareOfficerDetailVo();
        BeanUtils.copyProperties(threeWelfareOfficer, vo);
        vo.setBackgroundImage(UrlUtils.toAbsoluteUrl(threeWelfareOfficer.getBackgroundImage()));
        vo.setQrcodeImage(UrlUtils.toAbsoluteUrl(threeWelfareOfficer.getQrcodeImage()));
        return vo;
    }

    /**
     * 福利官薅羊毛新增
     *
     * @param threeWelfareOfficerValidate 参数
     */
    @Override
    public void add(ThreeWelfareOfficerValidate threeWelfareOfficerValidate) {
        ThreeWelfareOfficer threeWelfareOfficer = new ThreeWelfareOfficer();
        BeanUtils.copyProperties(threeWelfareOfficerValidate, threeWelfareOfficer);
        
        // 添加图片路径转换，将前端传来的绝对路径转换为相对路径
        threeWelfareOfficer.setBackgroundImage(UrlUtils.toRelativeUrl(threeWelfareOfficerValidate.getBackgroundImage()));
        threeWelfareOfficer.setQrcodeImage(UrlUtils.toRelativeUrl(threeWelfareOfficerValidate.getQrcodeImage()));
        
        threeWelfareOfficer.setIsDelete(0);
        threeWelfareOfficer.setCreateTime((int) (System.currentTimeMillis() / 1000));
        threeWelfareOfficer.setUpdateTime((int) (System.currentTimeMillis() / 1000));
        threeWelfareOfficerMapper.insert(threeWelfareOfficer);
    }

    /**
     * 福利官薅羊毛编辑
     *
     * @param id 主键ID
     * @param threeWelfareOfficerValidate 参数
     */
    @Override
    public void edit(Integer id, ThreeWelfareOfficerValidate threeWelfareOfficerValidate) {
        ThreeWelfareOfficer threeWelfareOfficer = threeWelfareOfficerMapper.selectById(id);
        if (threeWelfareOfficer == null) {
            throw new OperateException("数据不存在!");
        }

        BeanUtils.copyProperties(threeWelfareOfficerValidate, threeWelfareOfficer);
        
        // 添加图片路径转换，将前端传来的绝对路径转换为相对路径
        threeWelfareOfficer.setBackgroundImage(UrlUtils.toRelativeUrl(threeWelfareOfficerValidate.getBackgroundImage()));
        threeWelfareOfficer.setQrcodeImage(UrlUtils.toRelativeUrl(threeWelfareOfficerValidate.getQrcodeImage()));
        
        threeWelfareOfficer.setUpdateTime((int) (System.currentTimeMillis() / 1000));
        threeWelfareOfficerMapper.updateById(threeWelfareOfficer);
    }

    /**
     * 福利官薅羊毛删除
     *
     * @param id 主键ID
     */
    @Override
    public void del(Integer id) {
        ThreeWelfareOfficer threeWelfareOfficer = threeWelfareOfficerMapper.selectById(id);
        if (threeWelfareOfficer == null) {
            throw new OperateException("数据不存在!");
        }

        threeWelfareOfficer.setIsDelete(1);
        threeWelfareOfficer.setDeleteTime((int) (System.currentTimeMillis() / 1000));
        threeWelfareOfficerMapper.updateById(threeWelfareOfficer);
    }

    /**
     * 福利官薅羊毛状态更新
     *
     * @param id 主键ID
     */
    @Override
    public void change(Integer id) {
        ThreeWelfareOfficer threeWelfareOfficer = threeWelfareOfficerMapper.selectById(id);
        if (threeWelfareOfficer == null) {
            throw new OperateException("数据不存在!");
        }

        threeWelfareOfficer.setIsShow(threeWelfareOfficer.getIsShow() == 1 ? 0 : 1);
        threeWelfareOfficer.setUpdateTime((int) (System.currentTimeMillis() / 1000));
        threeWelfareOfficerMapper.updateById(threeWelfareOfficer);
    }
} 