package com.mdd.admin.service;

import com.mdd.admin.vo.member.MemberPointOrderListVo;
import com.mdd.admin.vo.member.MemberPointOrderDetailVo;
import com.mdd.common.core.PageResult;

import java.util.Map;

/**
 * 积分订单服务接口
 */
public interface IMemberPointOrderService {

    /**
     * 积分订单列表
     *
     * @param params 查询参数
     * @return PageResult<MemberPointOrderListVo>
     */
    PageResult<MemberPointOrderListVo> list(Map<String, String> params);

    /**
     * 积分订单详情
     *
     * @param id 主键ID
     * @return MemberPointOrderDetailVo
     */
    MemberPointOrderDetailVo detail(Integer id);

    /**
     * 积分订单删除
     *
     * @param id 主键ID
     */
    void del(Integer id);

} 