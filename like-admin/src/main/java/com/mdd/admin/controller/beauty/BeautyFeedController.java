package com.mdd.admin.controller.beauty;

import com.mdd.admin.service.beauty.IBeautyFeedService;
import com.mdd.admin.validate.beauty.BeautyFeedBindTopicValidate;
import com.mdd.admin.vo.beauty.BeautyFeedListVo;
import com.mdd.common.core.AjaxResult;
import com.mdd.common.core.PageResult;
import com.mdd.common.validator.annotation.IDMust;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 美妆社区帖子管理
 */
@RestController
@RequestMapping("api/beauty/feed")
@Api(tags = "美妆社区帖子管理")
public class BeautyFeedController {

    @Resource
    private IBeautyFeedService beautyFeedService;

    /**
     * 帖子列表
     */
    @GetMapping("/list")
    @ApiOperation(value="帖子列表")
    public AjaxResult<PageResult<BeautyFeedListVo>> list(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "15") Integer pageSize,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Integer userId) {
        PageResult<BeautyFeedListVo> list = beautyFeedService.list(pageNo, pageSize, title, userId);
        return AjaxResult.success(list);
    }

    /**
     * 帖子详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperation(value="帖子详情")
    public AjaxResult<BeautyFeedListVo> detail(
            @Validated @IDMust() @PathVariable("id") Integer id) {
        BeautyFeedListVo detail = beautyFeedService.detail(id);
        return AjaxResult.success(detail);
    }

    /**
     * 帖子删除
     */
    @PostMapping("/del/{id}")
    @ApiOperation(value="删除帖子")
    public AjaxResult<Object> del(@Validated @IDMust() @PathVariable("id") Integer id) {
        beautyFeedService.delete(id);
        return AjaxResult.success();
    }

    /**
     * 绑定话题到帖子
     */
    @PostMapping("/bind-topic")
    @ApiOperation(value="绑定话题", notes="将一个话题绑定到指定帖子，一个帖子只能绑定一个话题")
    public AjaxResult<Object> bindTopic(@Validated @RequestBody BeautyFeedBindTopicValidate validate) {
        beautyFeedService.bindTopic(validate);
        return AjaxResult.success("绑定成功");
    }
} 