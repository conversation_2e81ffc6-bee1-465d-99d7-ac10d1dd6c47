package com.mdd.admin.controller.three;

import com.mdd.admin.service.three.ILaThreeHomeVideoService;
import com.mdd.admin.validate.three.LaThreeHomeVideoVoValidate;
import com.mdd.admin.vo.three.LaThreeHomeVideoVo;
import com.mdd.common.core.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("api/three/homevideo")
public class LaThreeHomeVideoController {

    @Resource
    ILaThreeHomeVideoService iLaHomeVideoService;

    @GetMapping("/list")
    public AjaxResult<List<LaThreeHomeVideoVo>> list() {
        List<LaThreeHomeVideoVo> list = iLaHomeVideoService.list();
        return AjaxResult.success(list);
    }

    @PostMapping("/add")
    public AjaxResult<Object> add(@Validated @RequestBody LaThreeHomeVideoVoValidate validate) {
        iLaHomeVideoService.add(validate);
        return AjaxResult.success();
    }

    @PostMapping("/edit")
    public AjaxResult<Object> edit(@Validated @RequestBody LaThreeHomeVideoVoValidate validate) {
        iLaHomeVideoService.edit(validate);
        return AjaxResult.success();
    }

    @PostMapping("/del")
    public AjaxResult<Object> del(@RequestParam Integer id) {
        iLaHomeVideoService.del(id);
        return AjaxResult.success();
    }

    @PostMapping("/changeStatus")
    public AjaxResult<Object> changeStatus(@RequestParam Integer id) {
        iLaHomeVideoService.changeStatus(id);
        return AjaxResult.success();
    }

    @GetMapping("/detail")
    public AjaxResult<LaThreeHomeVideoVo> detail(@RequestParam Integer id) {
        LaThreeHomeVideoVo vo = iLaHomeVideoService.detail(id);
        return AjaxResult.success(vo);
    }
} 