import com.mdd.admin.LikeAdminApplication;
import com.mdd.common.exception.ErpApiException;
import com.mdd.common.plugin.wangdian.WdtDriver;
import com.mdd.common.plugin.wangdian.api.trade.ApiGoodsStockChangeQuery;
import com.mdd.common.plugin.wangdian.dto.response.ApiGoodsStockChangeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
@SpringBootTest(classes = LikeAdminApplication.class)
public class ErpApiGoodsStockChangeQueryTest {

    @Resource
    private WdtDriver wdtDriver;


    @Test
    public void query() {

        ApiGoodsStockChangeQueryResponse response = ApiGoodsStockChangeQuery.execute(wdtDriver, 100);

        if (response.getCode() != 0) {
            throw new ErpApiException(response.getMessage());
        } else {
            List<ApiGoodsStockChangeQueryResponse.StockChangeItem> stockChangeList = response.getStockChangeList();

            log.info(Arrays.toString(stockChangeList.toArray()));

            log.info("当前同步记录的条数：{}", response.getCurrentCount());
        }
    }

}
