import com.mdd.admin.LikeAdminApplication;
import com.mdd.common.exception.ErpApiException;
import com.mdd.common.plugin.wangdian.WdtDriver;
import com.mdd.common.plugin.wangdian.api.trade.LogisticsSyncQuery;
import com.mdd.common.plugin.wangdian.dto.request.LogisticsSyncQueryRequest;
import com.mdd.common.plugin.wangdian.dto.response.LogisticsSyncQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;

@Slf4j
@SpringBootTest(classes = LikeAdminApplication.class)
public class ErpLogisticsSyncQueryTest {

    @Resource
    private WdtDriver wdtDriver;


    @Test
    public void query() {
        LogisticsSyncQueryResponse response = LogisticsSyncQuery.execute(wdtDriver, new LogisticsSyncQueryRequest());

        if (response.getCode() != 0) {
            throw new ErpApiException(response.getMessage());
        } else {
            log.info("ERP物流同步成功，前同步记录的条数：{}，总条数：{}", response.getCurrentCount(), response.getTotalCount());

            System.out.printf(Arrays.toString(response.getTrades().toArray()));
        }
    }
}
